'use client';

// =====================================================
// ENUM MANAGER - ADMIN COMPONENT FOR MANAGING ENUMS
// =====================================================

import React, { useState, useEffect } from 'react';
import {
  EnumCategory,
  SystemEnum,
  CreateEnumRequest,
  UpdateEnumRequest,
  Language,
} from '@/lib/types/enum.types';
import { enumService } from '@/lib/services/enum.service';
import { useEnums } from '@/lib/contexts/EnumContext';

interface EnumManagerProps {
  className?: string;
}

export function EnumManager({ className = '' }: EnumManagerProps) {
  const { categories, refreshEnums, language, setLanguage } = useEnums();
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [categoryEnums, setCategoryEnums] = useState<SystemEnum[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingEnum, setEditingEnum] = useState<SystemEnum | null>(null);

  // Form state
  const [formData, setFormData] = useState<Partial<CreateEnumRequest>>({
    category_id: '',
    enum_key: '',
    display_name_en: '',
    display_name_vi: '',
    description_en: '',
    description_vi: '',
    sort_order: 0,
    color_code: '',
    icon_name: '',
    is_default: false,
  });

  // Load enums for selected category
  useEffect(() => {
    if (selectedCategory) {
      loadCategoryEnums(selectedCategory);
    }
  }, [selectedCategory]);

  const loadCategoryEnums = async (categoryId: string) => {
    try {
      setLoading(true);
      const enums = await enumService.getEnumsByCategory(categoryId, false); // Don't use cache
      setCategoryEnums(enums);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load enums');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEnum = async () => {
    try {
      if (!formData.category_id || !formData.enum_key || !formData.display_name_en || !formData.display_name_vi) {
        setError('Please fill in all required fields');
        return;
      }

      await enumService.createEnum(formData as CreateEnumRequest);
      await refreshEnums();
      await loadCategoryEnums(selectedCategory);
      
      // Reset form
      setFormData({
        category_id: selectedCategory,
        enum_key: '',
        display_name_en: '',
        display_name_vi: '',
        description_en: '',
        description_vi: '',
        sort_order: 0,
        color_code: '',
        icon_name: '',
        is_default: false,
      });
      setShowCreateForm(false);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create enum');
    }
  };

  const handleUpdateEnum = async () => {
    try {
      if (!editingEnum || !formData.display_name_en || !formData.display_name_vi) {
        setError('Please fill in all required fields');
        return;
      }

      await enumService.updateEnum({
        enum_id: editingEnum.enum_id,
        ...formData,
      } as UpdateEnumRequest);
      
      await refreshEnums();
      await loadCategoryEnums(selectedCategory);
      setEditingEnum(null);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update enum');
    }
  };

  const handleDeleteEnum = async (enumId: string) => {
    if (!confirm('Are you sure you want to delete this enum?')) return;

    try {
      await enumService.deleteEnum(enumId);
      await refreshEnums();
      await loadCategoryEnums(selectedCategory);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete enum');
    }
  };

  const startEdit = (enumItem: SystemEnum) => {
    setEditingEnum(enumItem);
    setFormData({
      category_id: enumItem.category_id,
      enum_key: enumItem.enum_key,
      display_name_en: enumItem.display_name_en,
      display_name_vi: enumItem.display_name_vi,
      description_en: enumItem.description_en || '',
      description_vi: enumItem.description_vi || '',
      sort_order: enumItem.sort_order,
      color_code: enumItem.color_code || '',
      icon_name: enumItem.icon_name || '',
      is_default: enumItem.is_default,
    });
    setShowCreateForm(true);
  };

  const cancelEdit = () => {
    setEditingEnum(null);
    setShowCreateForm(false);
    setFormData({
      category_id: selectedCategory,
      enum_key: '',
      display_name_en: '',
      display_name_vi: '',
      description_en: '',
      description_vi: '',
      sort_order: 0,
      color_code: '',
      icon_name: '',
      is_default: false,
    });
  };

  return (
    <div className={`enum-manager ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Enum Management</h2>
        <div className="flex gap-2">
          <select
            value={language}
            onChange={(e) => setLanguage(e.target.value as Language)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="vi">Tiếng Việt</option>
            <option value="en">English</option>
          </select>
          <button
            onClick={refreshEnums}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Category Selection */}
        <div className="lg:col-span-1">
          <h3 className="text-lg font-semibold mb-3">Categories</h3>
          <div className="space-y-2">
            {categories.map((category) => (
              <button
                key={category.category_id}
                onClick={() => setSelectedCategory(category.category_id)}
                className={`w-full text-left p-3 rounded-md border ${
                  selectedCategory === category.category_id
                    ? 'bg-blue-100 border-blue-500'
                    : 'bg-white border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="font-medium">
                  {language === 'en' ? category.display_name_en : category.display_name_vi}
                </div>
                <div className="text-sm text-gray-500">
                  {category.category_name} ({category.enum_count || 0} items)
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Enum List */}
        <div className="lg:col-span-2">
          {selectedCategory && (
            <>
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-lg font-semibold">
                  Enums for {categories.find(c => c.category_id === selectedCategory)?.category_name}
                </h3>
                <button
                  onClick={() => {
                    setFormData({ ...formData, category_id: selectedCategory });
                    setShowCreateForm(true);
                  }}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                >
                  Add Enum
                </button>
              </div>

              {/* Create/Edit Form */}
              {showCreateForm && (
                <div className="bg-gray-50 p-4 rounded-md mb-4">
                  <h4 className="font-medium mb-3">
                    {editingEnum ? 'Edit Enum' : 'Create New Enum'}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input
                      type="text"
                      placeholder="Enum Key (e.g., 'active')"
                      value={formData.enum_key}
                      onChange={(e) => setFormData({ ...formData, enum_key: e.target.value })}
                      disabled={!!editingEnum}
                      className="px-3 py-2 border rounded-md"
                    />
                    <input
                      type="number"
                      placeholder="Sort Order"
                      value={formData.sort_order}
                      onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
                      className="px-3 py-2 border rounded-md"
                    />
                    <input
                      type="text"
                      placeholder="Display Name (English)"
                      value={formData.display_name_en}
                      onChange={(e) => setFormData({ ...formData, display_name_en: e.target.value })}
                      className="px-3 py-2 border rounded-md"
                    />
                    <input
                      type="text"
                      placeholder="Display Name (Vietnamese)"
                      value={formData.display_name_vi}
                      onChange={(e) => setFormData({ ...formData, display_name_vi: e.target.value })}
                      className="px-3 py-2 border rounded-md"
                    />
                    <input
                      type="text"
                      placeholder="Color Code (e.g., #28a745)"
                      value={formData.color_code}
                      onChange={(e) => setFormData({ ...formData, color_code: e.target.value })}
                      className="px-3 py-2 border rounded-md"
                    />
                    <input
                      type="text"
                      placeholder="Icon Name"
                      value={formData.icon_name}
                      onChange={(e) => setFormData({ ...formData, icon_name: e.target.value })}
                      className="px-3 py-2 border rounded-md"
                    />
                    <textarea
                      placeholder="Description (English)"
                      value={formData.description_en}
                      onChange={(e) => setFormData({ ...formData, description_en: e.target.value })}
                      className="px-3 py-2 border rounded-md"
                    />
                    <textarea
                      placeholder="Description (Vietnamese)"
                      value={formData.description_vi}
                      onChange={(e) => setFormData({ ...formData, description_vi: e.target.value })}
                      className="px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div className="mt-4 flex items-center gap-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.is_default}
                        onChange={(e) => setFormData({ ...formData, is_default: e.target.checked })}
                        className="mr-2"
                      />
                      Is Default
                    </label>
                  </div>
                  <div className="mt-4 flex gap-2">
                    <button
                      onClick={editingEnum ? handleUpdateEnum : handleCreateEnum}
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                      {editingEnum ? 'Update' : 'Create'}
                    </button>
                    <button
                      onClick={cancelEdit}
                      className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Enum List */}
              {loading ? (
                <div className="text-center py-4">Loading...</div>
              ) : (
                <div className="space-y-2">
                  {categoryEnums.map((enumItem) => (
                    <div
                      key={enumItem.enum_id}
                      className="flex items-center justify-between p-3 bg-white border rounded-md"
                    >
                      <div className="flex items-center gap-3">
                        {enumItem.color_code && (
                          <div
                            className="w-4 h-4 rounded"
                            style={{ backgroundColor: enumItem.color_code }}
                          />
                        )}
                        <div>
                          <div className="font-medium">
                            {language === 'en' ? enumItem.display_name_en : enumItem.display_name_vi}
                          </div>
                          <div className="text-sm text-gray-500">
                            Key: {enumItem.enum_key}
                            {enumItem.is_default && <span className="ml-2 text-blue-500">(Default)</span>}
                            {enumItem.is_system && <span className="ml-2 text-red-500">(System)</span>}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={() => startEdit(enumItem)}
                          className="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600"
                        >
                          Edit
                        </button>
                        {!enumItem.is_system && (
                          <button
                            onClick={() => handleDeleteEnum(enumItem.enum_id)}
                            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                          >
                            Delete
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
