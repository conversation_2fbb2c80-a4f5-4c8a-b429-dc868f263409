const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// <PERSON><PERSON>u sắc cho console
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function manualDeleteUsers() {
  log('cyan', '🗑️  MANUAL DELETE USERS - ALTERNATIVE METHOD');
  
  try {
    // Tạo client với service role
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    log('blue', '📋 Lấy danh sách users từ profiles...');
    
    // L<PERSON>y danh sách users từ bảng profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, full_name, role');

    if (profilesError) {
      log('red', `❌ Lỗi khi lấy profiles: ${profilesError.message}`);
      return;
    }

    if (!profiles || profiles.length === 0) {
      log('yellow', '📭 Không có profiles nào trong database');
      return;
    }

    log('green', `✅ Tìm thấy ${profiles.length} profiles:`);
    profiles.forEach((profile, index) => {
      log('blue', `${index + 1}. ${profile.email} (${profile.role}) - ID: ${profile.id}`);
    });

    // Hướng dẫn xóa thủ công
    log('yellow', '\n📝 HƯỚNG DẪN XÓA USERS:');
    log('yellow', '1. Vào Supabase Dashboard: https://app.supabase.com');
    log('yellow', '2. Chọn project của bạn');
    log('yellow', '3. Vào Authentication → Users');
    log('yellow', '4. Tìm user theo email và xóa');
    log('yellow', '5. Hoặc sử dụng SQL Editor với câu lệnh sau:');
    
    console.log('\n-- SQL để xóa user cụ thể (thay YOUR_USER_ID):');
    console.log('-- DELETE FROM auth.users WHERE id = \'YOUR_USER_ID\';');
    
    console.log('\n-- SQL để xóa tất cả users (NGUY HIỂM!):');
    console.log('-- DELETE FROM auth.users;');

    // Thử xóa profiles trước (có thể giúp)
    log('cyan', '\n🗑️  Thử xóa profiles trước...');
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('Bạn có muốn xóa tất cả profiles? (y/N): ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        try {
          // Xóa các bảng phụ thuộc trước
          const tables = ['medical_records', 'prescriptions', 'appointments', 'patients', 'doctors', 'admins'];
          
          for (const table of tables) {
            try {
              const { error } = await supabase.from(table).delete().neq('id', 'dummy');
              if (error) {
                log('yellow', `⚠️  ${table}: ${error.message}`);
              } else {
                log('green', `✅ Đã xóa ${table}`);
              }
            } catch (err) {
              log('yellow', `⚠️  ${table} không tồn tại hoặc lỗi`);
            }
          }

          // Xóa profiles
          const { error: deleteError } = await supabase
            .from('profiles')
            .delete()
            .neq('id', 'dummy');

          if (deleteError) {
            log('red', `❌ Lỗi khi xóa profiles: ${deleteError.message}`);
          } else {
            log('green', '✅ Đã xóa tất cả profiles');
            log('yellow', '⚠️  Bây giờ bạn cần xóa auth.users thủ công qua Dashboard');
          }
        } catch (error) {
          log('red', `❌ Lỗi: ${error.message}`);
        }
      } else {
        log('blue', 'Đã hủy');
      }
      rl.close();
    });

  } catch (error) {
    log('red', `❌ Lỗi: ${error.message}`);
  }
}

manualDeleteUsers();
