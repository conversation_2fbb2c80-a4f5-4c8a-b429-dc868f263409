// =====================================================
// DYNAMIC ENUM SYSTEM - TYPESCRIPT TYPES
// =====================================================

export interface EnumCategory {
  category_id: string;
  category_name: string;
  display_name_en: string;
  display_name_vi: string;
  description?: string;
  is_system: boolean;
  is_active: boolean;
  enum_count?: number;
  created_at: string;
  updated_at: string;
}

export interface SystemEnum {
  enum_id: string;
  category_id: string;
  enum_key: string;
  display_name_en: string;
  display_name_vi: string;
  description_en?: string;
  description_vi?: string;
  sort_order: number;
  color_code?: string;
  icon_name?: string;
  is_default: boolean;
  is_system: boolean;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface SystemEnumWithCategory extends SystemEnum {
  category_name: string;
  category_display_en: string;
  category_display_vi: string;
}

// Language type for enum display
export type Language = 'vi' | 'en';

// Enum category constants
export const ENUM_CATEGORIES = {
  ROLE: 'ROLE',
  GENDER: 'GENDER',
  BLOOD_TYPE: 'BLOOD_TYPE',
  DOCTOR_STATUS: 'DOCTOR_STATUS',
  PATIENT_STATUS: 'PATIENT_STATUS',
  APPOINTMENT_TYPE: 'APPOINTMENT_TYPE',
  APPOINTMENT_STATUS: 'APPOINTMENT_STATUS',
  ROOM_TYPE: 'ROOM_TYPE',
  ROOM_STATUS: 'ROOM_STATUS',
  MEDICAL_RECORD_STATUS: 'MEDICAL_RECORD_STATUS',
  PRESCRIPTION_STATUS: 'PRESCRIPTION_STATUS',
} as const;

export type EnumCategoryType = typeof ENUM_CATEGORIES[keyof typeof ENUM_CATEGORIES];

// Enum option for UI components
export interface EnumOption {
  value: string;
  label: string;
  description?: string;
  color?: string;
  icon?: string;
  isDefault?: boolean;
}

// Enum service response types
export interface GetEnumsResponse {
  success: boolean;
  data: SystemEnum[];
  error?: string;
}

export interface GetEnumCategoriesResponse {
  success: boolean;
  data: EnumCategory[];
  error?: string;
}

// Form validation types
export interface EnumValidationRule {
  category: EnumCategoryType;
  required?: boolean;
  allowCustom?: boolean;
  customValidator?: (value: string) => boolean;
}

// Enum cache interface
export interface EnumCache {
  [categoryId: string]: {
    enums: SystemEnum[];
    lastUpdated: number;
    expiresAt: number;
  };
}

// Enum context interface for React context
export interface EnumContextType {
  enums: Record<string, SystemEnum[]>;
  categories: EnumCategory[];
  loading: boolean;
  error: string | null;
  language: Language;
  setLanguage: (lang: Language) => void;
  getEnumsByCategory: (categoryId: string) => SystemEnum[];
  getEnumOptions: (categoryId: string) => EnumOption[];
  getEnumDisplayName: (categoryId: string, enumKey: string) => string;
  getDefaultEnum: (categoryId: string) => SystemEnum | null;
  validateEnum: (categoryId: string, enumKey: string) => boolean;
  refreshEnums: () => Promise<void>;
}

// Enum management types (for admin)
export interface CreateEnumRequest {
  category_id: string;
  enum_key: string;
  display_name_en: string;
  display_name_vi: string;
  description_en?: string;
  description_vi?: string;
  sort_order?: number;
  color_code?: string;
  icon_name?: string;
  is_default?: boolean;
  metadata?: Record<string, any>;
}

export interface UpdateEnumRequest extends Partial<CreateEnumRequest> {
  enum_id: string;
}

export interface CreateEnumCategoryRequest {
  category_id: string;
  category_name: string;
  display_name_en: string;
  display_name_vi: string;
  description?: string;
}

export interface UpdateEnumCategoryRequest extends Partial<CreateEnumCategoryRequest> {
  category_id: string;
}

// Enum filter and search types
export interface EnumFilter {
  categoryId?: string;
  isActive?: boolean;
  isSystem?: boolean;
  search?: string;
  language?: Language;
}

export interface EnumSort {
  field: 'sort_order' | 'display_name_en' | 'display_name_vi' | 'created_at';
  direction: 'asc' | 'desc';
}

// Enum statistics
export interface EnumStatistics {
  totalCategories: number;
  totalEnums: number;
  systemEnums: number;
  customEnums: number;
  categoriesWithCounts: Array<{
    category_id: string;
    category_name: string;
    enum_count: number;
  }>;
}

// Legacy enum mapping (for backward compatibility)
export interface LegacyEnumMapping {
  [oldEnumValue: string]: {
    categoryId: string;
    newEnumKey: string;
  };
}

// Enum export/import types
export interface EnumExportData {
  categories: EnumCategory[];
  enums: SystemEnum[];
  exportedAt: string;
  version: string;
}

export interface EnumImportResult {
  success: boolean;
  imported: {
    categories: number;
    enums: number;
  };
  skipped: {
    categories: number;
    enums: number;
  };
  errors: string[];
}

// Enum audit log
export interface EnumAuditLog {
  log_id: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'ACTIVATE' | 'DEACTIVATE';
  entity_type: 'CATEGORY' | 'ENUM';
  entity_id: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  user_id: string;
  user_name: string;
  timestamp: string;
  ip_address?: string;
}

// Enum validation errors
export interface EnumValidationError {
  field: string;
  message: string;
  code: string;
}

export interface EnumValidationResult {
  isValid: boolean;
  errors: EnumValidationError[];
}

// Enum bulk operations
export interface BulkEnumOperation {
  operation: 'activate' | 'deactivate' | 'delete' | 'update_category';
  enumIds: string[];
  data?: Record<string, any>;
}

export interface BulkEnumResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: Array<{
    enumId: string;
    error: string;
  }>;
}
