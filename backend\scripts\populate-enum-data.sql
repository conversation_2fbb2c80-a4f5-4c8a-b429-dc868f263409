-- =====================================================
-- POPULATE DYNAMIC ENUM SYSTEM WITH INITIAL DATA
-- =====================================================
-- This script populates the enum_categories and system_enums tables
-- with all the current hardcoded enum values from the system

-- =====================================================
-- 1. INSERT ENUM CATEGORIES
-- =====================================================

INSERT INTO enum_categories (category_id, category_name, display_name_en, display_name_vi, description, is_system) VALUES
('ROLE', 'user_role', 'User Role', 'Vai trò người dùng', 'System user roles', true),
('GENDER', 'gender', 'Gender', 'Giới tính', 'Gender options', true),
('BLOOD_TYPE', 'blood_type', 'Blood Type', 'Nhóm máu', 'Blood type classifications', true),
('DOCTOR_STATUS', 'doctor_status', 'Doctor Status', 'Trạng thái bác sĩ', 'Doctor working status', true),
('PATIENT_STATUS', 'patient_status', 'Patient Status', 'Trạng thái bệnh nhân', 'Patient status in system', true),
('APPOINTMENT_TYPE', 'appointment_type', 'Appointment Type', 'Loại cuộc hẹn', 'Types of medical appointments', true),
('APPOINTMENT_STATUS', 'appointment_status', 'Appointment Status', 'Trạng thái cuộc hẹn', 'Status of appointments', true),
('ROOM_TYPE', 'room_type', 'Room Type', 'Loại phòng', 'Types of hospital rooms', true),
('ROOM_STATUS', 'room_status', 'Room Status', 'Trạng thái phòng', 'Room availability status', true),
('MEDICAL_RECORD_STATUS', 'medical_record_status', 'Medical Record Status', 'Trạng thái hồ sơ y tế', 'Status of medical records', true),
('PRESCRIPTION_STATUS', 'prescription_status', 'Prescription Status', 'Trạng thái đơn thuốc', 'Status of prescriptions', true)
ON CONFLICT (category_id) DO NOTHING;

-- =====================================================
-- 2. INSERT SYSTEM ENUMS - USER ROLES
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('ROLE', 'admin', 'Administrator', 'Quản trị viên', 'System administrator with full access', 'Quản trị viên hệ thống có quyền truy cập đầy đủ', 1, '#dc3545', 'shield-check', true),
('ROLE', 'doctor', 'Doctor', 'Bác sĩ', 'Medical doctor', 'Bác sĩ y khoa', 2, '#007bff', 'stethoscope', true),
('ROLE', 'patient', 'Patient', 'Bệnh nhân', 'Hospital patient', 'Bệnh nhân bệnh viện', 3, '#28a745', 'person', true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 3. INSERT SYSTEM ENUMS - GENDER
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('GENDER', 'male', 'Male', 'Nam', 'Male gender', 'Giới tính nam', 1, '#007bff', 'person-standing', true),
('GENDER', 'female', 'Female', 'Nữ', 'Female gender', 'Giới tính nữ', 2, '#e83e8c', 'person-dress', true),
('GENDER', 'other', 'Other', 'Khác', 'Other gender', 'Giới tính khác', 3, '#6c757d', 'person', true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 4. INSERT SYSTEM ENUMS - BLOOD TYPES
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, is_system) VALUES
('BLOOD_TYPE', 'A+', 'A+', 'A+', 'Blood type A positive', 'Nhóm máu A dương', 1, '#dc3545', true),
('BLOOD_TYPE', 'A-', 'A-', 'A-', 'Blood type A negative', 'Nhóm máu A âm', 2, '#dc3545', true),
('BLOOD_TYPE', 'B+', 'B+', 'B+', 'Blood type B positive', 'Nhóm máu B dương', 3, '#fd7e14', true),
('BLOOD_TYPE', 'B-', 'B-', 'B-', 'Blood type B negative', 'Nhóm máu B âm', 4, '#fd7e14', true),
('BLOOD_TYPE', 'AB+', 'AB+', 'AB+', 'Blood type AB positive', 'Nhóm máu AB dương', 5, '#6f42c1', true),
('BLOOD_TYPE', 'AB-', 'AB-', 'AB-', 'Blood type AB negative', 'Nhóm máu AB âm', 6, '#6f42c1', true),
('BLOOD_TYPE', 'O+', 'O+', 'O+', 'Blood type O positive', 'Nhóm máu O dương', 7, '#28a745', true),
('BLOOD_TYPE', 'O-', 'O-', 'O-', 'Blood type O negative', 'Nhóm máu O âm', 8, '#28a745', true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 5. INSERT SYSTEM ENUMS - DOCTOR STATUS
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('DOCTOR_STATUS', 'active', 'Active', 'Đang làm việc', 'Doctor is actively working', 'Bác sĩ đang làm việc', 1, '#28a745', 'check-circle', true, true),
('DOCTOR_STATUS', 'inactive', 'Inactive', 'Không hoạt động', 'Doctor is not currently working', 'Bác sĩ hiện không làm việc', 2, '#6c757d', 'x-circle', false, true),
('DOCTOR_STATUS', 'on_leave', 'On Leave', 'Đang nghỉ phép', 'Doctor is on leave', 'Bác sĩ đang nghỉ phép', 3, '#ffc107', 'clock', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 6. INSERT SYSTEM ENUMS - PATIENT STATUS
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('PATIENT_STATUS', 'active', 'Active', 'Đang điều trị', 'Patient is actively receiving care', 'Bệnh nhân đang được điều trị', 1, '#28a745', 'heart-pulse', true, true),
('PATIENT_STATUS', 'inactive', 'Inactive', 'Không hoạt động', 'Patient is not currently active', 'Bệnh nhân hiện không hoạt động', 2, '#6c757d', 'pause-circle', false, true),
('PATIENT_STATUS', 'deceased', 'Deceased', 'Đã qua đời', 'Patient has passed away', 'Bệnh nhân đã qua đời', 3, '#000000', 'x-circle', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 7. INSERT SYSTEM ENUMS - APPOINTMENT TYPES
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('APPOINTMENT_TYPE', 'consultation', 'Consultation', 'Khám bệnh', 'Regular medical consultation', 'Khám bệnh thường quy', 1, '#007bff', 'chat-dots', true, true),
('APPOINTMENT_TYPE', 'follow_up', 'Follow-up', 'Tái khám', 'Follow-up appointment', 'Cuộc hẹn tái khám', 2, '#17a2b8', 'arrow-repeat', false, true),
('APPOINTMENT_TYPE', 'emergency', 'Emergency', 'Cấp cứu', 'Emergency appointment', 'Cuộc hẹn cấp cứu', 3, '#dc3545', 'exclamation-triangle', false, true),
('APPOINTMENT_TYPE', 'surgery', 'Surgery', 'Phẫu thuật', 'Surgical procedure', 'Thủ thuật phẫu thuật', 4, '#6f42c1', 'scissors', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 8. INSERT SYSTEM ENUMS - APPOINTMENT STATUS
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('APPOINTMENT_STATUS', 'scheduled', 'Scheduled', 'Đã lên lịch', 'Appointment is scheduled', 'Cuộc hẹn đã được lên lịch', 1, '#ffc107', 'calendar-plus', true, true),
('APPOINTMENT_STATUS', 'confirmed', 'Confirmed', 'Đã xác nhận', 'Appointment is confirmed', 'Cuộc hẹn đã được xác nhận', 2, '#007bff', 'calendar-check', false, true),
('APPOINTMENT_STATUS', 'in_progress', 'In Progress', 'Đang diễn ra', 'Appointment is currently happening', 'Cuộc hẹn đang diễn ra', 3, '#17a2b8', 'clock', false, true),
('APPOINTMENT_STATUS', 'completed', 'Completed', 'Hoàn thành', 'Appointment has been completed', 'Cuộc hẹn đã hoàn thành', 4, '#28a745', 'check-circle', false, true),
('APPOINTMENT_STATUS', 'cancelled', 'Cancelled', 'Đã hủy', 'Appointment has been cancelled', 'Cuộc hẹn đã bị hủy', 5, '#dc3545', 'x-circle', false, true),
('APPOINTMENT_STATUS', 'no_show', 'No Show', 'Không đến', 'Patient did not show up', 'Bệnh nhân không đến', 6, '#6c757d', 'person-x', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 9. INSERT SYSTEM ENUMS - ROOM TYPES
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('ROOM_TYPE', 'consultation', 'Consultation Room', 'Phòng khám', 'Room for medical consultations', 'Phòng dành cho khám bệnh', 1, '#007bff', 'chat-dots', true),
('ROOM_TYPE', 'surgery', 'Surgery Room', 'Phòng mổ', 'Operating room for surgeries', 'Phòng mổ cho phẫu thuật', 2, '#dc3545', 'scissors', true),
('ROOM_TYPE', 'emergency', 'Emergency Room', 'Phòng cấp cứu', 'Emergency treatment room', 'Phòng điều trị cấp cứu', 3, '#ffc107', 'exclamation-triangle', true),
('ROOM_TYPE', 'ward', 'Ward', 'Phòng bệnh', 'Patient ward room', 'Phòng bệnh nhân', 4, '#28a745', 'house', true),
('ROOM_TYPE', 'icu', 'ICU', 'Phòng hồi sức', 'Intensive Care Unit', 'Phòng chăm sóc đặc biệt', 5, '#6f42c1', 'heart-pulse', true),
('ROOM_TYPE', 'laboratory', 'Laboratory', 'Phòng xét nghiệm', 'Medical laboratory', 'Phòng xét nghiệm y tế', 6, '#17a2b8', 'flask', true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 10. INSERT SYSTEM ENUMS - ROOM STATUS
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('ROOM_STATUS', 'available', 'Available', 'Trống', 'Room is available for use', 'Phòng có thể sử dụng', 1, '#28a745', 'check-circle', true, true),
('ROOM_STATUS', 'occupied', 'Occupied', 'Đang sử dụng', 'Room is currently occupied', 'Phòng đang được sử dụng', 2, '#dc3545', 'person-fill', false, true),
('ROOM_STATUS', 'maintenance', 'Maintenance', 'Bảo trì', 'Room is under maintenance', 'Phòng đang bảo trì', 3, '#ffc107', 'tools', false, true),
('ROOM_STATUS', 'out_of_service', 'Out of Service', 'Ngừng hoạt động', 'Room is out of service', 'Phòng ngừng hoạt động', 4, '#6c757d', 'x-circle', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 11. INSERT SYSTEM ENUMS - MEDICAL RECORD STATUS
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('MEDICAL_RECORD_STATUS', 'active', 'Active', 'Đang hoạt động', 'Medical record is active', 'Hồ sơ y tế đang hoạt động', 1, '#28a745', 'file-medical', true, true),
('MEDICAL_RECORD_STATUS', 'archived', 'Archived', 'Đã lưu trữ', 'Medical record is archived', 'Hồ sơ y tế đã được lưu trữ', 2, '#6c757d', 'archive', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 12. INSERT SYSTEM ENUMS - PRESCRIPTION STATUS
-- =====================================================

INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('PRESCRIPTION_STATUS', 'active', 'Active', 'Đang hiệu lực', 'Prescription is active', 'Đơn thuốc đang hiệu lực', 1, '#28a745', 'prescription', true, true),
('PRESCRIPTION_STATUS', 'dispensed', 'Dispensed', 'Đã cấp phát', 'Prescription has been dispensed', 'Đơn thuốc đã được cấp phát', 2, '#007bff', 'check-circle', false, true),
('PRESCRIPTION_STATUS', 'expired', 'Expired', 'Đã hết hạn', 'Prescription has expired', 'Đơn thuốc đã hết hạn', 3, '#ffc107', 'clock', false, true),
('PRESCRIPTION_STATUS', 'cancelled', 'Cancelled', 'Đã hủy', 'Prescription has been cancelled', 'Đơn thuốc đã bị hủy', 4, '#dc3545', 'x-circle', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- 13. CREATE VIEWS FOR EASY ACCESS
-- =====================================================

-- View to get all enum categories with counts
CREATE OR REPLACE VIEW v_enum_categories AS
SELECT
  ec.category_id,
  ec.category_name,
  ec.display_name_en,
  ec.display_name_vi,
  ec.description,
  ec.is_system,
  ec.is_active,
  COUNT(se.enum_id) as enum_count,
  ec.created_at,
  ec.updated_at
FROM enum_categories ec
LEFT JOIN system_enums se ON ec.category_id = se.category_id AND se.is_active = true
WHERE ec.is_active = true
GROUP BY ec.category_id, ec.category_name, ec.display_name_en, ec.display_name_vi,
         ec.description, ec.is_system, ec.is_active, ec.created_at, ec.updated_at
ORDER BY ec.category_name;

-- View to get all active enums with category info
CREATE OR REPLACE VIEW v_system_enums AS
SELECT
  se.enum_id,
  se.category_id,
  ec.category_name,
  ec.display_name_en as category_display_en,
  ec.display_name_vi as category_display_vi,
  se.enum_key,
  se.display_name_en,
  se.display_name_vi,
  se.description_en,
  se.description_vi,
  se.sort_order,
  se.color_code,
  se.icon_name,
  se.is_default,
  se.is_system,
  se.metadata,
  se.created_at,
  se.updated_at
FROM system_enums se
JOIN enum_categories ec ON se.category_id = ec.category_id
WHERE se.is_active = true AND ec.is_active = true
ORDER BY ec.category_name, se.sort_order, se.display_name_en;

-- =====================================================
-- 14. UTILITY FUNCTIONS
-- =====================================================

-- Function to validate enum value
CREATE OR REPLACE FUNCTION validate_enum_value(
  p_category_id TEXT,
  p_enum_key TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM system_enums
    WHERE category_id = p_category_id
      AND enum_key = p_enum_key
      AND is_active = true
  );
END;
$$ LANGUAGE plpgsql;

-- Function to get default enum value for a category
CREATE OR REPLACE FUNCTION get_default_enum_value(p_category_id TEXT)
RETURNS TEXT AS $$
DECLARE
  result TEXT;
BEGIN
  SELECT enum_key INTO result
  FROM system_enums
  WHERE category_id = p_category_id
    AND is_default = true
    AND is_active = true
  LIMIT 1;

  -- If no default found, return first active enum
  IF result IS NULL THEN
    SELECT enum_key INTO result
    FROM system_enums
    WHERE category_id = p_category_id
      AND is_active = true
    ORDER BY sort_order, enum_key
    LIMIT 1;
  END IF;

  RETURN result;
END;
$$ LANGUAGE plpgsql;
