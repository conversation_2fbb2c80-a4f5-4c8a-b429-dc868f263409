// =====================================================
// VÍ DỤ SỬ DỤNG HỆ THỐNG ENUM ĐỘNG
// =====================================================

'use client';

import React, { useState } from 'react';
import {
  useEnums,
  useRoleEnums,
  useGenderEnums,
  useBloodTypeEnums,
  useDoctorStatusEnums,
  useAppointmentStatusEnums,
  useEnumDisplayName,
  useEnumWithStyle,
  useEnumValidation,
} from '@/lib/contexts/EnumContext';
import { enumService } from '@/lib/services/enum.service';
import { ENUM_CATEGORIES } from '@/lib/types/enum.types';

// =====================================================
// VÍ DỤ 1: SỬ DỤNG HOOKS CƠ BẢN
// =====================================================

export function BasicEnumUsageExample() {
  const roleOptions = useRoleEnums();
  const genderOptions = useGenderEnums();
  const bloodTypeOptions = useBloodTypeEnums();

  const [selectedRole, setSelectedRole] = useState('');
  const [selectedGender, setSelectedGender] = useState('');
  const [selectedBloodType, setSelectedBloodType] = useState('');

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Ví dụ 1: Sử dụng Hooks cơ bản</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Role Selection */}
        <div>
          <label className="block text-sm font-medium mb-2">Vai trò</label>
          <select
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">Chọn vai trò</option>
            {roleOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Gender Selection */}
        <div>
          <label className="block text-sm font-medium mb-2">Giới tính</label>
          <select
            value={selectedGender}
            onChange={(e) => setSelectedGender(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">Chọn giới tính</option>
            {genderOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Blood Type Selection */}
        <div>
          <label className="block text-sm font-medium mb-2">Nhóm máu</label>
          <select
            value={selectedBloodType}
            onChange={(e) => setSelectedBloodType(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">Chọn nhóm máu</option>
            {bloodTypeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Display Selected Values */}
      <div className="mt-4 p-3 bg-gray-50 rounded">
        <p><strong>Đã chọn:</strong></p>
        <p>Vai trò: {selectedRole}</p>
        <p>Giới tính: {selectedGender}</p>
        <p>Nhóm máu: {selectedBloodType}</p>
      </div>
    </div>
  );
}

// =====================================================
// VÍ DỤ 2: SỬ DỤNG ENUM VỚI STYLE VÀ ICON
// =====================================================

export function StyledEnumExample() {
  const doctorStatusOptions = useDoctorStatusEnums();
  const appointmentStatusOptions = useAppointmentStatusEnums();

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Ví dụ 2: Enum với Style và Icon</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Doctor Status with Colors */}
        <div>
          <h4 className="font-medium mb-3">Trạng thái Bác sĩ</h4>
          <div className="space-y-2">
            {doctorStatusOptions.map((option) => (
              <div
                key={option.value}
                className="flex items-center gap-3 p-3 border rounded-md"
              >
                {option.color && (
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: option.color }}
                  />
                )}
                <span className="font-medium">{option.label}</span>
                {option.isDefault && (
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    Mặc định
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Appointment Status with Colors */}
        <div>
          <h4 className="font-medium mb-3">Trạng thái Cuộc hẹn</h4>
          <div className="space-y-2">
            {appointmentStatusOptions.map((option) => (
              <div
                key={option.value}
                className="flex items-center gap-3 p-3 border rounded-md"
              >
                {option.color && (
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: option.color }}
                  />
                )}
                <span className="font-medium">{option.label}</span>
                {option.description && (
                  <span className="text-sm text-gray-500">
                    - {option.description}
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// =====================================================
// VÍ DỤ 3: VALIDATION VÀ DISPLAY NAME
// =====================================================

export function ValidationExample() {
  const { getEnumDisplayName, validateEnum } = useEnums();
  const validateRole = useEnumValidation(ENUM_CATEGORIES.ROLE);
  
  const [testValue, setTestValue] = useState('');
  const [validationResult, setValidationResult] = useState<boolean | null>(null);
  const [displayName, setDisplayName] = useState('');

  const handleValidate = () => {
    const isValid = validateRole(testValue);
    setValidationResult(isValid);
    
    if (isValid) {
      const name = getEnumDisplayName(ENUM_CATEGORIES.ROLE, testValue);
      setDisplayName(name);
    } else {
      setDisplayName('');
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Ví dụ 3: Validation và Display Name</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            Nhập giá trị để kiểm tra (ví dụ: admin, doctor, patient)
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              value={testValue}
              onChange={(e) => setTestValue(e.target.value)}
              placeholder="Nhập enum value..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
            />
            <button
              onClick={handleValidate}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              Kiểm tra
            </button>
          </div>
        </div>

        {validationResult !== null && (
          <div className={`p-3 rounded-md ${
            validationResult 
              ? 'bg-green-100 border border-green-400 text-green-700'
              : 'bg-red-100 border border-red-400 text-red-700'
          }`}>
            {validationResult ? (
              <div>
                <p><strong>✅ Hợp lệ!</strong></p>
                <p>Tên hiển thị: <strong>{displayName}</strong></p>
              </div>
            ) : (
              <p><strong>❌ Không hợp lệ!</strong> Giá trị này không tồn tại trong enum ROLE.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// =====================================================
// VÍ DỤ 4: SỬ DỤNG ENUM SERVICE TRỰC TIẾP
// =====================================================

export function ServiceExample() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const testEnumService = async () => {
    setLoading(true);
    try {
      // Lấy enum options
      const roleOptions = await enumService.getEnumOptions(ENUM_CATEGORIES.ROLE, 'vi');
      
      // Lấy display name
      const displayName = await enumService.getEnumDisplayName(ENUM_CATEGORIES.ROLE, 'admin', 'vi');
      
      // Validate enum
      const isValid = await enumService.validateEnum(ENUM_CATEGORIES.ROLE, 'admin');
      
      // Lấy default enum
      const defaultEnum = await enumService.getDefaultEnum(ENUM_CATEGORIES.ROLE);
      
      setResult(`
        Role Options: ${roleOptions.length} items
        Admin Display Name: ${displayName}
        Is 'admin' valid: ${isValid}
        Default Role: ${defaultEnum?.enum_key || 'None'}
      `);
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Ví dụ 4: Sử dụng Enum Service</h3>
      
      <div className="space-y-4">
        <button
          onClick={testEnumService}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50"
        >
          {loading ? 'Đang test...' : 'Test Enum Service'}
        </button>

        {result && (
          <pre className="p-3 bg-gray-100 rounded-md text-sm whitespace-pre-wrap">
            {result}
          </pre>
        )}
      </div>
    </div>
  );
}

// =====================================================
// VÍ DỤ 5: ENUM TRONG FORM
// =====================================================

export function FormExample() {
  const [formData, setFormData] = useState({
    role: '',
    gender: '',
    bloodType: '',
    status: '',
  });

  const roleOptions = useRoleEnums();
  const genderOptions = useGenderEnums();
  const bloodTypeOptions = useBloodTypeEnums();
  const doctorStatusOptions = useDoctorStatusEnums();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form Data:', formData);
    alert('Form submitted! Check console for data.');
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Ví dụ 5: Enum trong Form</h3>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Vai trò *</label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({...formData, role: e.target.value})}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">Chọn vai trò</option>
              {roleOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Giới tính</label>
            <select
              value={formData.gender}
              onChange={(e) => setFormData({...formData, gender: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">Chọn giới tính</option>
              {genderOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Nhóm máu</label>
            <select
              value={formData.bloodType}
              onChange={(e) => setFormData({...formData, bloodType: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">Chọn nhóm máu</option>
              {bloodTypeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Trạng thái</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({...formData, status: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">Chọn trạng thái</option>
              {doctorStatusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <button
          type="submit"
          className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          Submit Form
        </button>
      </form>
    </div>
  );
}

// =====================================================
// COMPONENT TỔNG HỢP TẤT CẢ VÍ DỤ
// =====================================================

export function EnumUsageExamples() {
  return (
    <div className="space-y-8 p-8">
      <h1 className="text-3xl font-bold text-center mb-8">
        Ví dụ sử dụng Hệ thống Enum Động
      </h1>
      
      <BasicEnumUsageExample />
      <StyledEnumExample />
      <ValidationExample />
      <ServiceExample />
      <FormExample />
    </div>
  );
}
