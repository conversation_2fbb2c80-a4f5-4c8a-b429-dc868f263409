'use client';

// =====================================================
// ENUM CONTEXT - REACT CONTEXT FOR DYNAMIC ENUMS
// =====================================================

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import {
  EnumContextType,
  EnumCategory,
  SystemEnum,
  Language,
  EnumOption,
  ENUM_CATEGORIES,
} from '@/lib/types/enum.types';
import { enumService } from '@/lib/services/enum.service';

const EnumContext = createContext<EnumContextType | undefined>(undefined);

interface EnumProviderProps {
  children: React.ReactNode;
  defaultLanguage?: Language;
}

export function EnumProvider({ children, defaultLanguage = 'vi' }: EnumProviderProps) {
  const [enums, setEnums] = useState<Record<string, SystemEnum[]>>({});
  const [categories, setCategories] = useState<EnumCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [language, setLanguage] = useState<Language>(defaultLanguage);

  // Load enum categories
  const loadCategories = useCallback(async () => {
    try {
      const categoriesData = await enumService.getEnumCategories();
      setCategories(categoriesData);
    } catch (err) {
      console.error('Error loading enum categories:', err);
      setError(err instanceof Error ? err.message : 'Failed to load enum categories');
    }
  }, []);

  // Load enums for all categories
  const loadAllEnums = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load categories first
      await loadCategories();

      // Load enums for each category
      const enumsData: Record<string, SystemEnum[]> = {};
      
      // Load core enum categories
      const coreCategories = Object.values(ENUM_CATEGORIES);
      
      await Promise.all(
        coreCategories.map(async (categoryId) => {
          try {
            const categoryEnums = await enumService.getEnumsByCategory(categoryId);
            enumsData[categoryId] = categoryEnums;
          } catch (err) {
            console.error(`Error loading enums for category ${categoryId}:`, err);
            enumsData[categoryId] = [];
          }
        })
      );

      setEnums(enumsData);
    } catch (err) {
      console.error('Error loading enums:', err);
      setError(err instanceof Error ? err.message : 'Failed to load enums');
    } finally {
      setLoading(false);
    }
  }, [loadCategories]);

  // Refresh enums (clear cache and reload)
  const refreshEnums = useCallback(async () => {
    enumService.clearCache();
    await loadAllEnums();
  }, [loadAllEnums]);

  // Get enums by category
  const getEnumsByCategory = useCallback((categoryId: string): SystemEnum[] => {
    return enums[categoryId] || [];
  }, [enums]);

  // Get enum options for UI components
  const getEnumOptions = useCallback((categoryId: string): EnumOption[] => {
    const categoryEnums = getEnumsByCategory(categoryId);
    
    return categoryEnums.map(enumItem => ({
      value: enumItem.enum_key,
      label: language === 'en' ? enumItem.display_name_en : enumItem.display_name_vi,
      description: language === 'en' ? enumItem.description_en : enumItem.description_vi,
      color: enumItem.color_code,
      icon: enumItem.icon_name,
      isDefault: enumItem.is_default,
    }));
  }, [getEnumsByCategory, language]);

  // Get enum display name
  const getEnumDisplayName = useCallback((categoryId: string, enumKey: string): string => {
    const categoryEnums = getEnumsByCategory(categoryId);
    const enumItem = categoryEnums.find(e => e.enum_key === enumKey);
    
    if (!enumItem) return enumKey;
    
    return language === 'en' ? enumItem.display_name_en : enumItem.display_name_vi;
  }, [getEnumsByCategory, language]);

  // Get default enum for category
  const getDefaultEnum = useCallback((categoryId: string): SystemEnum | null => {
    const categoryEnums = getEnumsByCategory(categoryId);
    return categoryEnums.find(e => e.is_default) || categoryEnums[0] || null;
  }, [getEnumsByCategory]);

  // Validate enum value
  const validateEnum = useCallback((categoryId: string, enumKey: string): boolean => {
    const categoryEnums = getEnumsByCategory(categoryId);
    return categoryEnums.some(e => e.enum_key === enumKey);
  }, [getEnumsByCategory]);

  // Load enums on mount
  useEffect(() => {
    loadAllEnums();
  }, [loadAllEnums]);

  // Context value
  const contextValue: EnumContextType = {
    enums,
    categories,
    loading,
    error,
    language,
    setLanguage,
    getEnumsByCategory,
    getEnumOptions,
    getEnumDisplayName,
    getDefaultEnum,
    validateEnum,
    refreshEnums,
  };

  return (
    <EnumContext.Provider value={contextValue}>
      {children}
    </EnumContext.Provider>
  );
}

// Custom hook to use enum context
export function useEnums(): EnumContextType {
  const context = useContext(EnumContext);
  if (context === undefined) {
    throw new Error('useEnums must be used within an EnumProvider');
  }
  return context;
}

// Specialized hooks for common use cases
export function useEnumOptions(categoryId: string): EnumOption[] {
  const { getEnumOptions } = useEnums();
  return getEnumOptions(categoryId);
}

export function useEnumDisplayName(categoryId: string, enumKey: string): string {
  const { getEnumDisplayName } = useEnums();
  return getEnumDisplayName(categoryId, enumKey);
}

export function useDefaultEnum(categoryId: string): SystemEnum | null {
  const { getDefaultEnum } = useEnums();
  return getDefaultEnum(categoryId);
}

export function useEnumValidation(categoryId: string) {
  const { validateEnum } = useEnums();
  
  return useCallback((enumKey: string): boolean => {
    return validateEnum(categoryId, enumKey);
  }, [validateEnum, categoryId]);
}

// Hook for role-based enums
export function useRoleEnums() {
  return useEnumOptions(ENUM_CATEGORIES.ROLE);
}

// Hook for gender enums
export function useGenderEnums() {
  return useEnumOptions(ENUM_CATEGORIES.GENDER);
}

// Hook for blood type enums
export function useBloodTypeEnums() {
  return useEnumOptions(ENUM_CATEGORIES.BLOOD_TYPE);
}

// Hook for doctor status enums
export function useDoctorStatusEnums() {
  return useEnumOptions(ENUM_CATEGORIES.DOCTOR_STATUS);
}

// Hook for patient status enums
export function usePatientStatusEnums() {
  return useEnumOptions(ENUM_CATEGORIES.PATIENT_STATUS);
}

// Hook for appointment type enums
export function useAppointmentTypeEnums() {
  return useEnumOptions(ENUM_CATEGORIES.APPOINTMENT_TYPE);
}

// Hook for appointment status enums
export function useAppointmentStatusEnums() {
  return useEnumOptions(ENUM_CATEGORIES.APPOINTMENT_STATUS);
}

// Hook for room type enums
export function useRoomTypeEnums() {
  return useEnumOptions(ENUM_CATEGORIES.ROOM_TYPE);
}

// Hook for room status enums
export function useRoomStatusEnums() {
  return useEnumOptions(ENUM_CATEGORIES.ROOM_STATUS);
}

// Hook for medical record status enums
export function useMedicalRecordStatusEnums() {
  return useEnumOptions(ENUM_CATEGORIES.MEDICAL_RECORD_STATUS);
}

// Hook for prescription status enums
export function usePrescriptionStatusEnums() {
  return useEnumOptions(ENUM_CATEGORIES.PRESCRIPTION_STATUS);
}

// Hook for enum with color and icon support
export function useEnumWithStyle(categoryId: string, enumKey: string) {
  const { getEnumsByCategory } = useEnums();
  
  return React.useMemo(() => {
    const categoryEnums = getEnumsByCategory(categoryId);
    const enumItem = categoryEnums.find(e => e.enum_key === enumKey);
    
    if (!enumItem) return null;
    
    return {
      key: enumItem.enum_key,
      displayName: enumItem.display_name_vi,
      displayNameEn: enumItem.display_name_en,
      color: enumItem.color_code,
      icon: enumItem.icon_name,
      isDefault: enumItem.is_default,
      metadata: enumItem.metadata,
    };
  }, [getEnumsByCategory, categoryId, enumKey]);
}

// Hook for loading state
export function useEnumLoading(): boolean {
  const { loading } = useEnums();
  return loading;
}

// Hook for error state
export function useEnumError(): string | null {
  const { error } = useEnums();
  return error;
}
