# 🔄 Dynamic Enum System - Hospital Management

## 📋 Tổng quan

Hệ thống enum động cho phép quản lý các giá trị enum (như trạng thái, loại, v.v.) một cách linh hoạt mà không cần thay đổi code. Hệ thống hỗ trợ đa ngôn ngữ (Tiếng Việt + Tiếng Anh) và có thể được quản lý qua giao diện admin.

## 🚀 Triển khai

### Bước 1: Tạo bảng enum động
```sql
-- Chạy script tạo bảng
\i backend/scripts/setup-supabase-tables.sql
```

### Bước 2: Populate dữ liệu enum ban đầu
```sql
-- Chạy script populate dữ liệu
\i backend/scripts/populate-enum-data.sql
```

### Bước 3: Migration từ hardcoded enums
```sql
-- Chạy script migration (QUAN TRỌNG: Backup dữ liệu trước)
\i backend/scripts/migrate-to-dynamic-enums.sql
```

## 📊 Cấu trúc Database

### Bảng `enum_categories`
```sql
- category_id: TEXT (PK) - ID của category
- category_name: TEXT - Tên category (unique)
- display_name_en: TEXT - Tên hiển thị tiếng Anh
- display_name_vi: TEXT - Tên hiển thị tiếng Việt
- description: TEXT - Mô tả
- is_system: BOOLEAN - Category hệ thống (không thể xóa)
- is_active: BOOLEAN - Trạng thái hoạt động
```

### Bảng `system_enums`
```sql
- enum_id: TEXT (PK) - ID của enum
- category_id: TEXT (FK) - Tham chiếu đến enum_categories
- enum_key: TEXT - Key sử dụng trong code
- display_name_en: TEXT - Tên hiển thị tiếng Anh
- display_name_vi: TEXT - Tên hiển thị tiếng Việt
- description_en: TEXT - Mô tả tiếng Anh
- description_vi: TEXT - Mô tả tiếng Việt
- sort_order: INTEGER - Thứ tự sắp xếp
- color_code: TEXT - Mã màu cho UI
- icon_name: TEXT - Tên icon
- is_default: BOOLEAN - Giá trị mặc định
- is_system: BOOLEAN - Enum hệ thống (không thể xóa)
- is_active: BOOLEAN - Trạng thái hoạt động
- metadata: JSONB - Dữ liệu bổ sung
```

## 🎯 Các Category Enum Hiện tại

| Category ID | Tên | Mô tả |
|-------------|-----|-------|
| `ROLE` | User Role | Vai trò người dùng |
| `GENDER` | Gender | Giới tính |
| `BLOOD_TYPE` | Blood Type | Nhóm máu |
| `DOCTOR_STATUS` | Doctor Status | Trạng thái bác sĩ |
| `PATIENT_STATUS` | Patient Status | Trạng thái bệnh nhân |
| `APPOINTMENT_TYPE` | Appointment Type | Loại cuộc hẹn |
| `APPOINTMENT_STATUS` | Appointment Status | Trạng thái cuộc hẹn |
| `ROOM_TYPE` | Room Type | Loại phòng |
| `ROOM_STATUS` | Room Status | Trạng thái phòng |
| `MEDICAL_RECORD_STATUS` | Medical Record Status | Trạng thái hồ sơ y tế |
| `PRESCRIPTION_STATUS` | Prescription Status | Trạng thái đơn thuốc |

## 💻 Sử dụng trong Frontend

### 1. Sử dụng Context và Hooks

```tsx
import { useEnums, useRoleEnums, useGenderEnums } from '@/lib/contexts/EnumContext';

function MyComponent() {
  const { getEnumDisplayName, validateEnum } = useEnums();
  const roleOptions = useRoleEnums();
  const genderOptions = useGenderEnums();

  // Lấy tên hiển thị
  const displayName = getEnumDisplayName('ROLE', 'admin');
  
  // Validate enum
  const isValid = validateEnum('ROLE', 'admin');

  return (
    <select>
      {roleOptions.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}
```

### 2. Sử dụng Service trực tiếp

```tsx
import { enumService } from '@/lib/services/enum.service';

// Lấy enum options
const options = await enumService.getEnumOptions('ROLE', 'vi');

// Tạo enum mới
await enumService.createEnum({
  category_id: 'ROLE',
  enum_key: 'nurse',
  display_name_en: 'Nurse',
  display_name_vi: 'Y tá',
  sort_order: 4,
  color_code: '#17a2b8'
});
```

### 3. Component quản lý enum (Admin)

```tsx
import { EnumManager } from '@/components/admin/EnumManager';

function AdminPage() {
  return (
    <div>
      <h1>Admin Panel</h1>
      <EnumManager />
    </div>
  );
}
```

## 🔧 Functions và Views hữu ích

### Functions
```sql
-- Lấy tên hiển thị theo ngôn ngữ
SELECT get_enum_display_name('ROLE', 'admin', 'vi');

-- Lấy tất cả enum của một category
SELECT * FROM get_enums_by_category('ROLE', 'vi');

-- Validate enum value
SELECT validate_enum_value('ROLE', 'admin');

-- Lấy enum mặc định
SELECT get_default_enum_value('ROLE');
```

### Views
```sql
-- Xem tất cả categories với số lượng enum
SELECT * FROM v_enum_categories;

-- Xem tất cả enum với thông tin category
SELECT * FROM v_system_enums;

-- Enum options cho UI (Tiếng Việt)
SELECT * FROM v_enum_options_vi WHERE category_id = 'ROLE';

-- Enum options cho UI (Tiếng Anh)
SELECT * FROM v_enum_options_en WHERE category_id = 'ROLE';
```

## 🛡️ Bảo mật và Validation

### Row Level Security (RLS)
- Tất cả user có thể xem enum active
- Chỉ admin mới có thể thêm/sửa/xóa enum
- Enum system không thể bị xóa

### Validation Triggers
- Tự động validate enum values khi insert/update
- Thay thế CHECK constraints cũ
- Báo lỗi chi tiết khi enum không hợp lệ

## 📈 Lợi ích

### 1. Tính linh hoạt
- Thêm/sửa/xóa enum mà không cần deploy code
- Hỗ trợ đa ngôn ngữ
- Metadata tùy chỉnh

### 2. Quản lý tập trung
- Admin có thể quản lý tất cả enum
- Audit trail cho mọi thay đổi
- Backup và restore dễ dàng

### 3. Performance
- Cache system tích hợp
- Indexes được tối ưu
- Views để truy cập nhanh

### 4. UI/UX
- Color coding cho enum
- Icon support
- Sort order tùy chỉnh

## 🔄 Migration từ Hardcoded Enums

### Trước migration:
```sql
-- CHECK constraint cũ
role TEXT NOT NULL CHECK (role IN ('admin', 'doctor', 'patient'))
```

### Sau migration:
```sql
-- Validation trigger mới
CREATE TRIGGER trigger_validate_profile_role
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION validate_profile_role();
```

## 🚨 Lưu ý quan trọng

1. **Backup dữ liệu** trước khi chạy migration
2. **Test thoroughly** trên môi trường dev trước
3. **Enum system** không nên bị xóa
4. **Cache** sẽ tự động clear khi có thay đổi
5. **RLS policies** đảm bảo bảo mật

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong Supabase
2. Verify RLS policies
3. Check enum data integrity
4. Clear cache nếu cần thiết

## 🔮 Tương lai

Có thể mở rộng:
- Enum versioning
- Import/Export enum data
- Enum usage analytics
- Custom validation rules
- Workflow approval cho enum changes
