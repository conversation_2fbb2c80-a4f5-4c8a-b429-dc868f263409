const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Format patterns for validation
const PATTERNS = {
  DOCTOR_ID: /^DOC\d{6}$/,
  PATIENT_ID: /^PAT\d{6}$/,
  APPOINTMENT_ID: /^APT\d{6}$/,
  DEPARTMENT_ID: /^DEPT\d{6}$/,
  ROOM_ID: /^ROOM\d{6}$/,
  MEDICAL_RECORD_ID: /^MED\d{6}$/,
  PHONE_VN: /^0[0-9]{9}$/,
  LICENSE_VN: /^VN-[A-Z]{2,4}-\d{4}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
};

// Generate correct format ID
function generateCorrectId(prefix, sequence) {
  return `${prefix}${String(sequence).padStart(6, '0')}`;
}

// Fix phone number to Vietnam format
function fixPhoneNumber(phone) {
  if (!phone) return null;
  
  const digits = phone.replace(/\D/g, '');
  
  if (digits.length === 10 && digits.startsWith('0')) {
    return digits;
  } else if (digits.length === 11 && digits.startsWith('84')) {
    return '0' + digits.substring(2);
  } else if (digits.length === 9) {
    return '0' + digits;
  } else if (digits.length === 10 && !digits.startsWith('0')) {
    return '0' + digits.substring(1);
  }
  
  return phone;
}

// Fix license number to VN-XX-1234 format
function fixLicenseNumber(license) {
  if (!license) return 'VN-BS-0001';
  
  const letters = license.match(/[A-Z]+/g)?.join('') || 'BS';
  const numbers = license.match(/\d+/g)?.join('') || '0001';
  
  const prefix = letters.substring(0, 4);
  const suffix = numbers.padStart(4, '0').substring(0, 4);
  
  return `VN-${prefix}-${suffix}`;
}

// Convert Vietnamese values to English
const VIETNAMESE_TO_ENGLISH = {
  // Specialties
  'tim_mach': 'cardiology',
  'than_kinh': 'neurology',
  'nhi_khoa': 'pediatrics',
  'san_phu_khoa': 'obstetrics_gynecology',
  'ngoai_khoa': 'surgery',
  'noi_khoa': 'internal_medicine',
  'mat': 'ophthalmology',
  'tai_mui_hong': 'ent',
  'da_lieu': 'dermatology',
  'tam_than': 'psychiatry',
  'ung_buou': 'oncology',
  'gay_me_hoi_suc': 'anesthesiology',
  
  // Status
  'dang_lam': 'active',
  'nghi_phep': 'on_leave',
  'tam_nghi': 'inactive',
  'nghi_huu': 'retired',
  'dang_dieu_tri': 'active',
  'xuat_vien': 'discharged',
  'chuyen_vien': 'transferred',
  'tu_vong': 'deceased',
  'dat_lich': 'scheduled',
  'xac_nhan': 'confirmed',
  'dang_kham': 'in_progress',
  'hoan_thanh': 'completed',
  'huy_bo': 'cancelled',
  'vang_mat': 'no_show',
  
  // Gender
  'nam': 'male',
  'nu': 'female',
  'khac': 'other',
  
  // Room types
  'phong_kham': 'consultation',
  'phong_mo': 'surgery',
  'phong_cap_cuu': 'emergency',
  'phong_benh': 'ward',
  'phong_hoi_suc': 'icu',
  'phong_xet_nghiem': 'laboratory'
};

async function createBackup() {
  log('cyan', '📦 Creating comprehensive backup...');
  
  try {
    const tables = ['doctors', 'patients', 'appointments', 'departments', 'rooms', 'medical_records'];
    const backup = {
      timestamp: new Date().toISOString(),
      migration_type: 'format_fix_all',
      tables: {}
    };
    
    for (const table of tables) {
      const { data, error } = await supabase.from(table).select('*');
      if (error) {
        log('yellow', `⚠️  Could not backup ${table}: ${error.message}`);
        backup.tables[table] = [];
      } else {
        backup.tables[table] = data || [];
        log('blue', `   ✅ ${table}: ${data?.length || 0} records`);
      }
    }
    
    const fs = require('fs');
    const backupFile = `format-fix-backup-${Date.now()}.json`;
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    log('green', `✅ Backup created: ${backupFile}`);
    return backupFile;
  } catch (error) {
    log('red', `❌ Backup failed: ${error.message}`);
    throw error;
  }
}

async function fixDoctorsTable() {
  log('cyan', '\n👨‍⚕️ Fixing doctors table...');
  
  try {
    const { data: doctors } = await supabase.from('doctors').select('*');
    
    for (let i = 0; i < doctors.length; i++) {
      const doctor = doctors[i];
      const updates = {};
      let needsUpdate = false;
      
      // Fix doctor_id format
      if (!PATTERNS.DOCTOR_ID.test(doctor.doctor_id)) {
        updates.doctor_id = generateCorrectId('DOC', i + 1);
        needsUpdate = true;
        log('yellow', `   ID: ${doctor.doctor_id} → ${updates.doctor_id}`);
      }
      
      // Fix phone number
      if (doctor.phone_number && !PATTERNS.PHONE_VN.test(doctor.phone_number)) {
        updates.phone_number = fixPhoneNumber(doctor.phone_number);
        needsUpdate = true;
        log('yellow', `   Phone: ${doctor.phone_number} → ${updates.phone_number}`);
      }
      
      // Fix license number
      if (doctor.license_number && !PATTERNS.LICENSE_VN.test(doctor.license_number)) {
        updates.license_number = fixLicenseNumber(doctor.license_number);
        needsUpdate = true;
        log('yellow', `   License: ${doctor.license_number} → ${updates.license_number}`);
      }
      
      // Fix specialty (Vietnamese to English)
      if (doctor.specialty && VIETNAMESE_TO_ENGLISH[doctor.specialty]) {
        updates.specialty = VIETNAMESE_TO_ENGLISH[doctor.specialty];
        needsUpdate = true;
        log('yellow', `   Specialty: ${doctor.specialty} → ${updates.specialty}`);
      }
      
      // Fix status (Vietnamese to English)
      if (doctor.status && VIETNAMESE_TO_ENGLISH[doctor.status]) {
        updates.status = VIETNAMESE_TO_ENGLISH[doctor.status];
        needsUpdate = true;
        log('yellow', `   Status: ${doctor.status} → ${updates.status}`);
      }
      
      // Fix email format
      if (doctor.email && !PATTERNS.EMAIL.test(doctor.email)) {
        // Try to fix common email issues
        let fixedEmail = doctor.email.toLowerCase().trim();
        if (!fixedEmail.includes('@')) {
          fixedEmail = `${fixedEmail}@hospital.vn`;
        }
        updates.email = fixedEmail;
        needsUpdate = true;
        log('yellow', `   Email: ${doctor.email} → ${updates.email}`);
      }
      
      if (needsUpdate) {
        // Delete old record and insert new one to handle ID changes
        await supabase.from('doctors').delete().eq('doctor_id', doctor.doctor_id);
        
        const newDoctor = { ...doctor, ...updates };
        const { error } = await supabase.from('doctors').insert(newDoctor);
        
        if (error) {
          log('red', `❌ Failed to fix doctor ${doctor.doctor_id}: ${error.message}`);
        } else {
          log('green', `✅ Fixed doctor ${doctor.doctor_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Doctors table fix failed: ${error.message}`);
  }
}

async function fixPatientsTable() {
  log('cyan', '\n🏥 Fixing patients table...');
  
  try {
    const { data: patients } = await supabase.from('patients').select('*');
    
    for (let i = 0; i < patients.length; i++) {
      const patient = patients[i];
      const updates = {};
      let needsUpdate = false;
      
      // Fix patient_id format
      if (!PATTERNS.PATIENT_ID.test(patient.patient_id)) {
        updates.patient_id = generateCorrectId('PAT', i + 1);
        needsUpdate = true;
        log('yellow', `   ID: ${patient.patient_id} → ${updates.patient_id}`);
      }
      
      // Fix phone number
      if (patient.phone_number && !PATTERNS.PHONE_VN.test(patient.phone_number)) {
        updates.phone_number = fixPhoneNumber(patient.phone_number);
        needsUpdate = true;
        log('yellow', `   Phone: ${patient.phone_number} → ${updates.phone_number}`);
      }
      
      // Fix gender (Vietnamese to English)
      if (patient.gender && VIETNAMESE_TO_ENGLISH[patient.gender]) {
        updates.gender = VIETNAMESE_TO_ENGLISH[patient.gender];
        needsUpdate = true;
        log('yellow', `   Gender: ${patient.gender} → ${updates.gender}`);
      }
      
      // Fix status (Vietnamese to English)
      if (patient.status && VIETNAMESE_TO_ENGLISH[patient.status]) {
        updates.status = VIETNAMESE_TO_ENGLISH[patient.status];
        needsUpdate = true;
        log('yellow', `   Status: ${patient.status} → ${updates.status}`);
      }
      
      // Fix email format
      if (patient.email && !PATTERNS.EMAIL.test(patient.email)) {
        let fixedEmail = patient.email.toLowerCase().trim();
        if (!fixedEmail.includes('@')) {
          fixedEmail = `${fixedEmail}@gmail.com`;
        }
        updates.email = fixedEmail;
        needsUpdate = true;
        log('yellow', `   Email: ${patient.email} → ${updates.email}`);
      }
      
      if (needsUpdate) {
        await supabase.from('patients').delete().eq('patient_id', patient.patient_id);
        
        const newPatient = { ...patient, ...updates };
        const { error } = await supabase.from('patients').insert(newPatient);
        
        if (error) {
          log('red', `❌ Failed to fix patient ${patient.patient_id}: ${error.message}`);
        } else {
          log('green', `✅ Fixed patient ${patient.patient_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Patients table fix failed: ${error.message}`);
  }
}

async function fixDepartmentsTable() {
  log('cyan', '\n🏢 Fixing departments table...');
  
  try {
    const { data: departments } = await supabase.from('departments').select('*');
    
    for (let i = 0; i < departments.length; i++) {
      const dept = departments[i];
      const updates = {};
      let needsUpdate = false;
      
      // Fix department_id format
      if (!PATTERNS.DEPARTMENT_ID.test(dept.department_id)) {
        updates.department_id = generateCorrectId('DEPT', i + 1);
        needsUpdate = true;
        log('yellow', `   ID: ${dept.department_id} → ${updates.department_id}`);
      }
      
      // Fix phone number
      if (dept.phone_number && !PATTERNS.PHONE_VN.test(dept.phone_number)) {
        updates.phone_number = fixPhoneNumber(dept.phone_number);
        needsUpdate = true;
        log('yellow', `   Phone: ${dept.phone_number} → ${updates.phone_number}`);
      }
      
      // Fix email format
      if (dept.email && !PATTERNS.EMAIL.test(dept.email)) {
        let fixedEmail = dept.email.toLowerCase().trim();
        if (!fixedEmail.includes('@')) {
          fixedEmail = `${fixedEmail}@hospital.vn`;
        }
        updates.email = fixedEmail;
        needsUpdate = true;
        log('yellow', `   Email: ${dept.email} → ${updates.email}`);
      }
      
      if (needsUpdate) {
        await supabase.from('departments').delete().eq('department_id', dept.department_id);
        
        const newDept = { ...dept, ...updates };
        const { error } = await supabase.from('departments').insert(newDept);
        
        if (error) {
          log('red', `❌ Failed to fix department ${dept.department_id}: ${error.message}`);
        } else {
          log('green', `✅ Fixed department ${dept.department_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Departments table fix failed: ${error.message}`);
  }
}

async function fixRoomsTable() {
  log('cyan', '\n🏠 Fixing rooms table...');
  
  try {
    const { data: rooms } = await supabase.from('rooms').select('*');
    
    for (let i = 0; i < rooms.length; i++) {
      const room = rooms[i];
      const updates = {};
      let needsUpdate = false;
      
      // Fix room_id format
      if (!PATTERNS.ROOM_ID.test(room.room_id)) {
        updates.room_id = generateCorrectId('ROOM', i + 1);
        needsUpdate = true;
        log('yellow', `   ID: ${room.room_id} → ${updates.room_id}`);
      }
      
      // Fix room type (Vietnamese to English)
      if (room.room_type && VIETNAMESE_TO_ENGLISH[room.room_type]) {
        updates.room_type = VIETNAMESE_TO_ENGLISH[room.room_type];
        needsUpdate = true;
        log('yellow', `   Type: ${room.room_type} → ${updates.room_type}`);
      }
      
      // Fix status (Vietnamese to English)
      if (room.status && VIETNAMESE_TO_ENGLISH[room.status]) {
        updates.status = VIETNAMESE_TO_ENGLISH[room.status];
        needsUpdate = true;
        log('yellow', `   Status: ${room.status} → ${updates.status}`);
      }
      
      if (needsUpdate) {
        await supabase.from('rooms').delete().eq('room_id', room.room_id);
        
        const newRoom = { ...room, ...updates };
        const { error } = await supabase.from('rooms').insert(newRoom);
        
        if (error) {
          log('red', `❌ Failed to fix room ${room.room_id}: ${error.message}`);
        } else {
          log('green', `✅ Fixed room ${room.room_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Rooms table fix failed: ${error.message}`);
  }
}

async function fixAppointmentsTable() {
  log('cyan', '\n📅 Fixing appointments table...');
  
  try {
    const { data: appointments } = await supabase.from('appointments').select('*');
    
    for (let i = 0; i < appointments.length; i++) {
      const apt = appointments[i];
      const updates = {};
      let needsUpdate = false;
      
      // Fix appointment_id format
      if (!PATTERNS.APPOINTMENT_ID.test(apt.appointment_id)) {
        updates.appointment_id = generateCorrectId('APT', i + 1);
        needsUpdate = true;
        log('yellow', `   ID: ${apt.appointment_id} → ${updates.appointment_id}`);
      }
      
      // Fix status (Vietnamese to English)
      if (apt.status && VIETNAMESE_TO_ENGLISH[apt.status]) {
        updates.status = VIETNAMESE_TO_ENGLISH[apt.status];
        needsUpdate = true;
        log('yellow', `   Status: ${apt.status} → ${updates.status}`);
      }
      
      // Fix type (Vietnamese to English)
      if (apt.type && VIETNAMESE_TO_ENGLISH[apt.type]) {
        updates.type = VIETNAMESE_TO_ENGLISH[apt.type];
        needsUpdate = true;
        log('yellow', `   Type: ${apt.type} → ${updates.type}`);
      }
      
      if (needsUpdate) {
        await supabase.from('appointments').delete().eq('appointment_id', apt.appointment_id);
        
        const newApt = { ...apt, ...updates };
        const { error } = await supabase.from('appointments').insert(newApt);
        
        if (error) {
          log('red', `❌ Failed to fix appointment ${apt.appointment_id}: ${error.message}`);
        } else {
          log('green', `✅ Fixed appointment ${apt.appointment_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Appointments table fix failed: ${error.message}`);
  }
}

async function fixMedicalRecordsTable() {
  log('cyan', '\n📋 Fixing medical records table...');
  
  try {
    const { data: records } = await supabase.from('medical_records').select('*');
    
    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const updates = {};
      let needsUpdate = false;
      
      // Fix record_id format
      if (!PATTERNS.MEDICAL_RECORD_ID.test(record.record_id)) {
        updates.record_id = generateCorrectId('MED', i + 1);
        needsUpdate = true;
        log('yellow', `   ID: ${record.record_id} → ${updates.record_id}`);
      }
      
      // Fix status (Vietnamese to English)
      if (record.status && VIETNAMESE_TO_ENGLISH[record.status]) {
        updates.status = VIETNAMESE_TO_ENGLISH[record.status];
        needsUpdate = true;
        log('yellow', `   Status: ${record.status} → ${updates.status}`);
      }
      
      if (needsUpdate) {
        await supabase.from('medical_records').delete().eq('record_id', record.record_id);
        
        const newRecord = { ...record, ...updates };
        const { error } = await supabase.from('medical_records').insert(newRecord);
        
        if (error) {
          log('red', `❌ Failed to fix medical record ${record.record_id}: ${error.message}`);
        } else {
          log('green', `✅ Fixed medical record ${record.record_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Medical records table fix failed: ${error.message}`);
  }
}

async function verifyAllFormats() {
  log('cyan', '\n🔍 Verifying all format fixes...');
  
  const checks = [
    { table: 'doctors', pattern: PATTERNS.DOCTOR_ID, field: 'doctor_id' },
    { table: 'patients', pattern: PATTERNS.PATIENT_ID, field: 'patient_id' },
    { table: 'appointments', pattern: PATTERNS.APPOINTMENT_ID, field: 'appointment_id' },
    { table: 'departments', pattern: PATTERNS.DEPARTMENT_ID, field: 'department_id' },
    { table: 'rooms', pattern: PATTERNS.ROOM_ID, field: 'room_id' },
    { table: 'medical_records', pattern: PATTERNS.MEDICAL_RECORD_ID, field: 'record_id' }
  ];
  
  let totalRecords = 0;
  let compliantRecords = 0;
  
  for (const check of checks) {
    try {
      const { data } = await supabase.from(check.table).select('*');
      const invalidRecords = data?.filter(record => !check.pattern.test(record[check.field])) || [];
      const validCount = (data?.length || 0) - invalidRecords.length;
      
      totalRecords += data?.length || 0;
      compliantRecords += validCount;
      
      if (invalidRecords.length === 0) {
        log('green', `   ✅ ${check.table}: All ${data?.length || 0} records compliant`);
      } else {
        log('red', `   ❌ ${check.table}: ${invalidRecords.length} non-compliant`);
      }
    } catch (error) {
      log('red', `   ❌ ${check.table}: Verification failed`);
    }
  }
  
  const complianceRate = ((compliantRecords / totalRecords) * 100).toFixed(1);
  
  log('cyan', `\n📊 FINAL COMPLIANCE REPORT`);
  log('cyan', `==========================`);
  log('blue', `Total Records: ${totalRecords}`);
  log('green', `Compliant Records: ${compliantRecords}`);
  log('magenta', `Compliance Rate: ${complianceRate}%`);
  
  return complianceRate >= 95;
}

async function main() {
  log('cyan', '🔧 Hospital Management - Fix All Format Issues');
  log('cyan', '===============================================');
  
  try {
    const backupFile = await createBackup();
    
    await fixDoctorsTable();
    await fixPatientsTable();
    await fixDepartmentsTable();
    await fixRoomsTable();
    await fixAppointmentsTable();
    await fixMedicalRecordsTable();
    
    const success = await verifyAllFormats();
    
    if (success) {
      log('green', '\n🎉 All format issues fixed successfully!');
      log('blue', `📦 Backup: ${backupFile}`);
      log('cyan', '\n✅ All data now complies with standards:');
      log('yellow', '   - IDs: Correct format (DOC000001, PAT000001, etc.)');
      log('yellow', '   - Phone: Vietnam format (**********)');
      log('yellow', '   - License: VN-XX-1234 format');
      log('yellow', '   - Email: Valid email format');
      log('yellow', '   - Status: English values for API consistency');
      log('yellow', '   - Specialty: English values for integration');
    } else {
      log('yellow', '\n⚠️  Some issues may remain. Check details above.');
    }
    
  } catch (error) {
    log('red', `❌ Format fix failed: ${error.message}`);
    process.exit(1);
  }
}

main();
