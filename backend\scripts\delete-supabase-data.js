#!/usr/bin/env node

/**
 * Script để xóa dữ liệu trong Supabase
 * Chạy: node backend/scripts/delete-supabase-data.js
 */

require('dotenv').config({ path: './backend/.env' });
const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Khởi tạo Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Interface để nhận input từ user
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

async function confirmAction(message) {
  const answer = await askQuestion(`${message} (y/N): `);
  return answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes';
}

async function checkConnection() {
  log('blue', '🔗 Kiểm tra kết nối Supabase...');

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Lỗi kết nối: ${error.message}`);
    }

    log('green', '✅ Kết nối Supabase thành công');
    return true;
  } catch (error) {
    log('red', `❌ Lỗi kết nối: ${error.message}`);
    return false;
  }
}

async function getTableCounts() {
  log('cyan', '\n📊 Kiểm tra số lượng dữ liệu hiện tại...');

  const tables = [
    'profiles', 'doctors', 'patients', 'appointments',
    'medical_records', 'prescriptions', 'departments', 'rooms'
  ];

  const views = [
    'doctor_details', 'patient_details', 'appointment_details'
  ];

  const counts = {};

  // Kiểm tra bảng thực tế
  for (const table of tables) {
    try {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });

      if (!error) {
        counts[table] = count || 0;
        log('blue', `  📋 ${table}: ${count || 0} records`);
      }
    } catch (err) {
      counts[table] = 'N/A';
      log('yellow', `  ⚠️  ${table}: Không thể truy cập`);
    }
  }

  // Kiểm tra views
  log('cyan', '\n📊 Kiểm tra dữ liệu trong VIEWs...');
  for (const view of views) {
    try {
      const { count, error } = await supabase
        .from(view)
        .select('*', { count: 'exact', head: true });

      if (!error) {
        counts[view] = count || 0;
        log('blue', `  👁️  ${view} (VIEW): ${count || 0} records`);
      }
    } catch (err) {
      counts[view] = 'N/A';
      log('yellow', `  ⚠️  ${view} (VIEW): Không thể truy cập`);
    }
  }

  return counts;
}

async function deleteTableData(tableName) {
  try {
    log('yellow', `🗑️  Đang xóa dữ liệu từ bảng ${tableName}...`);

    const { error } = await supabase
      .from(tableName)
      .delete()
      .neq('id', 'dummy'); // Xóa tất cả records

    if (error) {
      throw error;
    }

    log('green', `✅ Đã xóa dữ liệu từ bảng ${tableName}`);
    return true;
  } catch (error) {
    log('red', `❌ Lỗi khi xóa ${tableName}: ${error.message}`);
    return false;
  }
}

async function deleteAllData() {
  log('cyan', '\n🗑️  Bắt đầu xóa dữ liệu...');

  // Thứ tự xóa để tránh foreign key constraint errors
  const deletionOrder = [
    'medical_records',
    'prescriptions',
    'appointments',
    'patients',
    'doctors',
    'admins',
    'rooms',
    'departments',
    'profiles'
  ];

  let successCount = 0;

  for (const table of deletionOrder) {
    const success = await deleteTableData(table);
    if (success) successCount++;
  }

  log('cyan', `\n📊 Kết quả: ${successCount}/${deletionOrder.length} bảng đã được xóa thành công`);

  return successCount === deletionOrder.length;
}

async function deleteSpecificTable(tableName) {
  log('cyan', `\n🗑️  Xóa dữ liệu từ bảng ${tableName}...`);

  const success = await deleteTableData(tableName);

  if (success) {
    log('green', `✅ Đã xóa thành công dữ liệu từ bảng ${tableName}`);
  }

  return success;
}

async function deleteProfilesOnly() {
  log('cyan', '\n🗑️  Xóa chỉ bảng profiles...');

  // Cảnh báo về foreign key constraints
  log('yellow', '⚠️  Lưu ý: Xóa profiles có thể ảnh hưởng đến các bảng khác do foreign key constraints');

  const confirmed = await confirmAction('Bạn có chắc chắn muốn tiếp tục?');
  if (!confirmed) {
    log('blue', 'Đã hủy thao tác');
    return false;
  }

  return await deleteSpecificTable('profiles');
}

async function deleteDoctorDetails() {
  log('cyan', '\n🗑️  Xóa dữ liệu doctor_details (VIEW)...');
  log('yellow', '⚠️  Lưu ý: doctor_details là VIEW, sẽ xóa dữ liệu từ bảng doctors và profiles (role=doctor)');

  const confirmed = await confirmAction('Bạn có chắc chắn muốn xóa tất cả dữ liệu bác sĩ?');
  if (!confirmed) {
    log('blue', 'Đã hủy thao tác');
    return false;
  }

  try {
    // Xóa từ bảng doctors trước
    const doctorsSuccess = await deleteTableData('doctors');

    // Xóa profiles có role = 'doctor'
    log('yellow', '🗑️  Đang xóa profiles có role = doctor...');
    const { error } = await supabase
      .from('profiles')
      .delete()
      .eq('role', 'doctor');

    if (error) {
      log('red', `❌ Lỗi khi xóa doctor profiles: ${error.message}`);
      return false;
    }

    log('green', '✅ Đã xóa thành công tất cả dữ liệu bác sĩ');
    return doctorsSuccess;
  } catch (error) {
    log('red', `❌ Lỗi: ${error.message}`);
    return false;
  }
}

async function deletePatientDetails() {
  log('cyan', '\n🗑️  Xóa dữ liệu patient_details (VIEW)...');
  log('yellow', '⚠️  Lưu ý: patient_details là VIEW, sẽ xóa dữ liệu từ bảng patients và profiles (role=patient)');

  const confirmed = await confirmAction('Bạn có chắc chắn muốn xóa tất cả dữ liệu bệnh nhân?');
  if (!confirmed) {
    log('blue', 'Đã hủy thao tác');
    return false;
  }

  try {
    // Xóa các bảng phụ thuộc trước
    log('yellow', '🗑️  Đang xóa dữ liệu phụ thuộc...');
    await deleteTableData('medical_records');
    await deleteTableData('appointments');

    // Xóa từ bảng patients
    const patientsSuccess = await deleteTableData('patients');

    // Xóa profiles có role = 'patient'
    log('yellow', '🗑️  Đang xóa profiles có role = patient...');
    const { error } = await supabase
      .from('profiles')
      .delete()
      .eq('role', 'patient');

    if (error) {
      log('red', `❌ Lỗi khi xóa patient profiles: ${error.message}`);
      return false;
    }

    log('green', '✅ Đã xóa thành công tất cả dữ liệu bệnh nhân');
    return patientsSuccess;
  } catch (error) {
    log('red', `❌ Lỗi: ${error.message}`);
    return false;
  }
}

async function main() {
  try {
    log('cyan', '🏥 SCRIPT XÓA DỮ LIỆU SUPABASE - HOSPITAL MANAGEMENT SYSTEM');
    log('red', '⚠️  CẢNH BÁO: Script này sẽ xóa dữ liệu vĩnh viễn!');

    // Kiểm tra kết nối
    const connected = await checkConnection();
    if (!connected) {
      process.exit(1);
    }

    // Hiển thị số lượng dữ liệu hiện tại
    await getTableCounts();

    // Menu lựa chọn
    console.log('\n📋 Lựa chọn hành động:');
    console.log('1. Xóa TẤT CẢ dữ liệu (tất cả bảng)');
    console.log('2. Xóa chỉ bảng PROFILES');
    console.log('3. Xóa dữ liệu DOCTOR_DETAILS (VIEW)');
    console.log('4. Xóa dữ liệu PATIENT_DETAILS (VIEW)');
    console.log('5. Xóa bảng cụ thể');
    console.log('6. Hủy bỏ');

    const choice = await askQuestion('\nNhập lựa chọn (1-6): ');

    switch (choice) {
      case '1':
        log('red', '\n⚠️  BẠN SẮP XÓA TẤT CẢ DỮ LIỆU!');
        const confirmAll = await confirmAction('Bạn có CHẮC CHẮN muốn xóa tất cả dữ liệu?');
        if (confirmAll) {
          await deleteAllData();
        } else {
          log('blue', 'Đã hủy thao tác');
        }
        break;

      case '2':
        await deleteProfilesOnly();
        break;

      case '3':
        await deleteDoctorDetails();
        break;

      case '4':
        await deletePatientDetails();
        break;

      case '5':
        const tableName = await askQuestion('Nhập tên bảng muốn xóa: ');
        if (tableName.trim()) {
          const confirmTable = await confirmAction(`Xóa dữ liệu từ bảng "${tableName}"?`);
          if (confirmTable) {
            await deleteSpecificTable(tableName.trim());
          } else {
            log('blue', 'Đã hủy thao tác');
          }
        }
        break;

      case '6':
        log('blue', 'Đã hủy thao tác');
        break;

      default:
        log('red', 'Lựa chọn không hợp lệ');
        break;
    }

    // Hiển thị kết quả cuối
    log('cyan', '\n📊 Kiểm tra dữ liệu sau khi xóa:');
    await getTableCounts();

  } catch (error) {
    log('red', `❌ Lỗi: ${error.message}`);
  } finally {
    rl.close();
  }
}

// Chạy script
if (require.main === module) {
  main();
}

module.exports = {
  deleteAllData,
  deleteSpecificTable,
  deleteDoctorDetails,
  deletePatientDetails,
  getTableCounts
};
