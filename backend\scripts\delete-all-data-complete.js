#!/usr/bin/env node

/**
 * <PERSON><PERSON>t tổng hợp để xóa TOÀN BỘ dữ liệu bao gồm cả auth users
 * Chạy: node backend/scripts/delete-all-data-complete.js
 */

const path = require('path');
require('dotenv').config({
  path: path.resolve(__dirname, '../.env')
});
const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Kiểm tra biến môi trường
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('- SUPABASE_URL:', !!supabaseUrl);
  console.error('- SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  console.error('\n💡 Đảm bảo file backend/.env có các biến sau:');
  console.error('SUPABASE_URL=your_supabase_url');
  console.error('SUPABASE_SERVICE_ROLE_KEY=your_service_role_key');
  process.exit(1);
}

// Khởi tạo Supabase client với Service Role Key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Interface để nhận input từ user
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

function confirmAction(message) {
  return new Promise((resolve) => {
    rl.question(`${message} (y/N): `, (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function checkConnection() {
  log('blue', '🔗 Kiểm tra kết nối Supabase...');

  try {
    // Test kết nối với profiles table
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    // Test kết nối với auth admin
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();

    if (authError) {
      throw new Error(`Lỗi auth admin: ${authError.message}`);
    }

    log('green', '✅ Kết nối Supabase thành công');
    log('blue', `📊 Tìm thấy ${authData.users.length} auth users`);
    return true;
  } catch (error) {
    log('red', `❌ Lỗi kết nối: ${error.message}`);
    log('yellow', '⚠️  Đảm bảo bạn đã cấu hình SUPABASE_SERVICE_ROLE_KEY');
    return false;
  }
}

async function getDataCounts() {
  log('cyan', '\n📊 Kiểm tra số lượng dữ liệu hiện tại...');

  const tables = [
    'profiles', 'doctors', 'patients', 'appointments',
    'medical_records', 'prescriptions', 'departments', 'rooms'
  ];

  const counts = {};

  // Kiểm tra bảng
  for (const table of tables) {
    try {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });

      if (!error) {
        counts[table] = count || 0;
        log('blue', `  📋 ${table}: ${count || 0} records`);
      }
    } catch (err) {
      counts[table] = 'N/A';
      log('yellow', `  ⚠️  ${table}: Không thể truy cập`);
    }
  }

  // Kiểm tra auth users
  try {
    const { data, error } = await supabase.auth.admin.listUsers();
    if (!error) {
      counts['auth.users'] = data.users.length;
      log('blue', `  🔐 auth.users: ${data.users.length} users`);
    }
  } catch (err) {
    counts['auth.users'] = 'N/A';
    log('yellow', '  ⚠️  auth.users: Không thể truy cập');
  }

  return counts;
}

async function deleteTableData(tableName) {
  try {
    log('yellow', `🗑️  Đang xóa dữ liệu từ bảng ${tableName}...`);

    const { error } = await supabase
      .from(tableName)
      .delete()
      .neq('id', 'dummy');

    if (error) {
      throw error;
    }

    log('green', `✅ Đã xóa dữ liệu từ bảng ${tableName}`);
    return true;
  } catch (error) {
    log('red', `❌ Lỗi khi xóa ${tableName}: ${error.message}`);
    return false;
  }
}

async function deleteAllAuthUsers() {
  try {
    log('yellow', '🗑️  Đang xóa tất cả auth users...');

    const { data, error } = await supabase.auth.admin.listUsers();

    if (error) throw error;

    if (data.users.length === 0) {
      log('yellow', '📭 Không có auth users nào để xóa');
      return true;
    }

    let successCount = 0;
    let failCount = 0;

    for (const user of data.users) {
      try {
        const { error: deleteError } = await supabase.auth.admin.deleteUser(user.id);

        if (deleteError) {
          log('red', `❌ Lỗi xóa user ${user.email}: ${deleteError.message}`);
          failCount++;
        } else {
          log('green', `✅ Đã xóa auth user: ${user.email || user.id}`);
          successCount++;
        }
      } catch (err) {
        log('red', `❌ Lỗi xóa user ${user.email}: ${err.message}`);
        failCount++;
      }
    }

    log('cyan', `📊 Kết quả xóa auth users: ${successCount} thành công, ${failCount} thất bại`);
    return failCount === 0;
  } catch (error) {
    log('red', `❌ Lỗi khi xóa auth users: ${error.message}`);
    return false;
  }
}

async function deleteAllDataComplete() {
  log('cyan', '\n🗑️  Bắt đầu xóa TOÀN BỘ dữ liệu...');
  log('red', '⚠️  THAO TÁC NÀY SẼ XÓA TẤT CẢ DỮ LIỆU VÀ AUTH USERS!');
  log('red', '⚠️  KHÔNG THỂ HOÀN TÁC!');

  // Thứ tự xóa an toàn
  const deletionOrder = [
    'medical_records',
    'prescriptions',
    'appointments',
    'patients',
    'doctors',
    'admins',
    'rooms',
    'departments',
    'profiles'
  ];

  let allSuccess = true;

  // Bước 1: Xóa dữ liệu từ các bảng
  log('cyan', '\n📋 Bước 1: Xóa dữ liệu từ các bảng...');
  for (const table of deletionOrder) {
    const success = await deleteTableData(table);
    if (!success) allSuccess = false;
  }

  // Bước 2: Xóa auth users
  log('cyan', '\n🔐 Bước 2: Xóa auth users...');
  const authSuccess = await deleteAllAuthUsers();
  if (!authSuccess) allSuccess = false;

  if (allSuccess) {
    log('green', '\n🎉 Đã xóa thành công TOÀN BỘ dữ liệu!');
  } else {
    log('yellow', '\n⚠️  Hoàn thành với một số lỗi. Kiểm tra logs ở trên.');
  }

  return allSuccess;
}

async function main() {
  try {
    log('cyan', '🏥 SCRIPT XÓA TOÀN BỘ DỮ LIỆU - HOSPITAL MANAGEMENT SYSTEM');
    log('red', '⚠️  CẢNH BÁO: Script này sẽ xóa TẤT CẢ dữ liệu và auth users!');
    log('red', '⚠️  BAO GỒM: Profiles, Doctors, Patients, Auth Users, v.v.');

    // Kiểm tra kết nối
    const connected = await checkConnection();
    if (!connected) {
      process.exit(1);
    }

    // Hiển thị số lượng dữ liệu hiện tại
    await getDataCounts();

    // Xác nhận cuối cùng
    console.log('\n🚨 CẢNH BÁO CUỐI CÙNG:');
    console.log('- Tất cả dữ liệu trong database sẽ bị xóa');
    console.log('- Tất cả auth users sẽ bị xóa');
    console.log('- Thao tác này KHÔNG THỂ HOÀN TÁC');
    console.log('- Bạn sẽ cần tạo lại tài khoản và dữ liệu từ đầu');

    const finalConfirm = await confirmAction('\nBạn có THỰC SỰ CHẮC CHẮN muốn tiếp tục?');
    if (!finalConfirm) {
      log('blue', 'Đã hủy thao tác. Dữ liệu được giữ nguyên.');
      return;
    }

    const doubleConfirm = await confirmAction('Xác nhận lần cuối - XÓA TẤT CẢ?');
    if (!doubleConfirm) {
      log('blue', 'Đã hủy thao tác. Dữ liệu được giữ nguyên.');
      return;
    }

    // Thực hiện xóa
    await deleteAllDataComplete();

    // Hiển thị kết quả cuối
    log('cyan', '\n📊 Kiểm tra dữ liệu sau khi xóa:');
    await getDataCounts();

    log('cyan', '\n💡 Để khôi phục dữ liệu:');
    log('blue', '1. Chạy script tạo sample data');
    log('blue', '2. Hoặc import từ backup');
    log('blue', '3. Tạo lại tài khoản qua signup process');

  } catch (error) {
    log('red', `❌ Lỗi: ${error.message}`);
  } finally {
    rl.close();
  }
}

// Chạy script
if (require.main === module) {
  main();
}

module.exports = {
  deleteAllDataComplete,
  deleteAllAuthUsers,
  getDataCounts
};
