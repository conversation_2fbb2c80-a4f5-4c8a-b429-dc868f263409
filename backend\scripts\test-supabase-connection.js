#!/usr/bin/env node

/**
 * Script để test kết nối Supabase và kiểm tra Service Role Key
 */

const path = require('path');
require('dotenv').config({ 
  path: path.resolve(__dirname, '../.env')
});
const { createClient } = require('@supabase/supabase-js');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testConnection() {
  log('cyan', '🔍 KIỂM TRA KẾT NỐI SUPABASE');
  
  // Kiểm tra biến môi trường
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  log('blue', '\n📋 Kiểm tra biến môi trường:');
  log('blue', `SUPABASE_URL: ${supabaseUrl ? '✅ Có' : '❌ Thiếu'}`);
  log('blue', `SUPABASE_ANON_KEY: ${supabaseAnonKey ? '✅ Có' : '❌ Thiếu'}`);
  log('blue', `SUPABASE_SERVICE_ROLE_KEY: ${supabaseServiceKey ? '✅ Có' : '❌ Thiếu'}`);
  
  if (!supabaseUrl || !supabaseServiceKey) {
    log('red', '\n❌ Thiếu biến môi trường cần thiết!');
    return false;
  }
  
  // Test với Anon Key
  log('cyan', '\n🔗 Test kết nối với Anon Key...');
  try {
    const anonClient = createClient(supabaseUrl, supabaseAnonKey);
    const { data, error } = await anonClient.from('profiles').select('count').limit(1);
    
    if (error) {
      log('yellow', `⚠️  Anon Key: ${error.message}`);
    } else {
      log('green', '✅ Anon Key: Kết nối thành công');
    }
  } catch (err) {
    log('red', `❌ Anon Key: ${err.message}`);
  }
  
  // Test với Service Role Key
  log('cyan', '\n🔐 Test kết nối với Service Role Key...');
  try {
    const serviceClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Test truy cập profiles
    log('blue', '📋 Test truy cập bảng profiles...');
    const { data: profilesData, error: profilesError } = await serviceClient
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (profilesError) {
      log('yellow', `⚠️  Profiles: ${profilesError.message}`);
    } else {
      log('green', '✅ Profiles: Truy cập thành công');
    }
    
    // Test truy cập auth admin
    log('blue', '🔐 Test truy cập auth admin...');
    const { data: authData, error: authError } = await serviceClient.auth.admin.listUsers();
    
    if (authError) {
      log('red', `❌ Auth Admin: ${authError.message}`);
      log('yellow', '💡 Có thể Service Role Key không đúng hoặc không có quyền admin');
      return false;
    } else {
      log('green', `✅ Auth Admin: Thành công - Tìm thấy ${authData.users.length} users`);
      
      // Hiển thị một vài users đầu tiên
      if (authData.users.length > 0) {
        log('blue', '\n👥 Danh sách users:');
        authData.users.slice(0, 3).forEach((user, index) => {
          log('blue', `${index + 1}. ${user.email || 'No email'} (${user.id})`);
        });
        if (authData.users.length > 3) {
          log('blue', `... và ${authData.users.length - 3} users khác`);
        }
      }
      
      return true;
    }
  } catch (err) {
    log('red', `❌ Service Role: ${err.message}`);
    return false;
  }
}

async function testSpecificOperations() {
  log('cyan', '\n🧪 Test các thao tác cụ thể...');
  
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  const client = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
  
  try {
    // Test đếm profiles
    log('blue', '📊 Đếm profiles...');
    const { count: profileCount, error: countError } = await client
      .from('profiles')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      log('red', `❌ Lỗi đếm profiles: ${countError.message}`);
    } else {
      log('green', `✅ Profiles count: ${profileCount}`);
    }
    
    // Test đếm doctors
    log('blue', '👨‍⚕️ Đếm doctors...');
    const { count: doctorCount, error: doctorError } = await client
      .from('doctors')
      .select('*', { count: 'exact', head: true });
    
    if (doctorError) {
      log('yellow', `⚠️  Doctors: ${doctorError.message}`);
    } else {
      log('green', `✅ Doctors count: ${doctorCount}`);
    }
    
    // Test đếm patients
    log('blue', '🏥 Đếm patients...');
    const { count: patientCount, error: patientError } = await client
      .from('patients')
      .select('*', { count: 'exact', head: true });
    
    if (patientError) {
      log('yellow', `⚠️  Patients: ${patientError.message}`);
    } else {
      log('green', `✅ Patients count: ${patientCount}`);
    }
    
  } catch (err) {
    log('red', `❌ Lỗi test operations: ${err.message}`);
  }
}

async function main() {
  try {
    const connectionOk = await testConnection();
    
    if (connectionOk) {
      await testSpecificOperations();
      log('green', '\n🎉 Tất cả test đều thành công!');
      log('blue', '💡 Bạn có thể chạy script delete-auth-users.js');
    } else {
      log('red', '\n❌ Có lỗi kết nối. Kiểm tra lại cấu hình.');
      log('yellow', '\n💡 Các bước kiểm tra:');
      log('yellow', '1. Kiểm tra SUPABASE_URL trong .env');
      log('yellow', '2. Kiểm tra SUPABASE_SERVICE_ROLE_KEY trong .env');
      log('yellow', '3. Đảm bảo Service Role Key có quyền admin');
      log('yellow', '4. Kiểm tra kết nối internet');
    }
  } catch (error) {
    log('red', `❌ Lỗi: ${error.message}`);
  }
}

if (require.main === module) {
  main();
}
