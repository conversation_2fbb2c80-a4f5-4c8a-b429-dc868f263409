#!/usr/bin/env node

/**
 * Script debug biến môi trường
 */

const path = require('path');
const fs = require('fs');

console.log('🔍 DEBUG BIẾN MÔI TRƯỜNG\n');

// Ki<PERSON>m tra đường dẫn file .env
const envPath = path.resolve(__dirname, '../.env');
console.log('📁 Đường dẫn .env:', envPath);
console.log('📄 File .env tồn tại:', fs.existsSync(envPath) ? '✅' : '❌');

if (fs.existsSync(envPath)) {
  console.log('\n📋 Nội dung file .env:');
  const envContent = fs.readFileSync(envPath, 'utf8');
  const lines = envContent.split('\n');
  
  lines.forEach((line, index) => {
    if (line.trim() && !line.startsWith('#')) {
      const [key, value] = line.split('=');
      if (key && value) {
        const maskedValue = key.includes('KEY') || key.includes('SECRET') ? 
          value.substring(0, 10) + '...' : value;
        console.log(`${index + 1}. ${key}=${maskedValue}`);
      }
    }
  });
}

// Load dotenv
require('dotenv').config({ path: envPath });

console.log('\n🔧 Biến môi trường sau khi load:');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ Có' : '❌ Thiếu');
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? '✅ Có' : '❌ Thiếu');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Có' : '❌ Thiếu');

if (process.env.SUPABASE_URL) {
  console.log('\n🌐 URL Details:');
  console.log('URL:', process.env.SUPABASE_URL);
  
  try {
    const url = new URL(process.env.SUPABASE_URL);
    console.log('Protocol:', url.protocol);
    console.log('Host:', url.host);
    console.log('Valid URL:', '✅');
  } catch (err) {
    console.log('Valid URL:', '❌', err.message);
  }
}

if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY;
  console.log('\n🔑 Service Role Key Details:');
  console.log('Length:', key.length);
  console.log('Starts with:', key.substring(0, 10) + '...');
  console.log('Format looks like JWT:', key.includes('.') ? '✅' : '❌');
}
