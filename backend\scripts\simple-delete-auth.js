#!/usr/bin/env node

/**
 * Script đơn giản để xóa auth users
 */

const path = require('path');
require('dotenv').config({ 
  path: path.resolve(__dirname, '../.env')
});

const { createClient } = require('@supabase/supabase-js');

async function main() {
  console.log('🔐 SIMPLE DELETE AUTH USERS\n');
  
  // Kiểm tra env vars
  const url = process.env.SUPABASE_URL;
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  console.log('URL:', url ? 'OK' : 'MISSING');
  console.log('KEY:', key ? 'OK' : 'MISSING');
  
  if (!url || !key) {
    console.log('\n❌ Missing environment variables');
    return;
  }
  
  // Tạo client
  const supabase = createClient(url, key, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
  
  try {
    console.log('\n🔍 Testing connection...');
    
    // Test 1: Basic table access
    console.log('Test 1: Profiles table...');
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (profileError) {
      console.log('❌ Profiles error:', profileError.message);
    } else {
      console.log('✅ Profiles OK');
    }
    
    // Test 2: Auth admin
    console.log('Test 2: Auth admin...');
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.log('❌ Auth error:', authError.message);
      console.log('Error details:', JSON.stringify(authError, null, 2));
    } else {
      console.log('✅ Auth OK - Found', authData.users.length, 'users');
      
      // Hiển thị users
      if (authData.users.length > 0) {
        console.log('\n👥 Users:');
        authData.users.forEach((user, i) => {
          console.log(`${i + 1}. ${user.email || 'No email'} (${user.id})`);
        });
        
        // Hỏi có muốn xóa không
        const readline = require('readline');
        const rl = readline.createInterface({
          input: process.stdin,
          output: process.stdout
        });
        
        const answer = await new Promise(resolve => {
          rl.question('\nDelete all users? (y/N): ', resolve);
        });
        
        if (answer.toLowerCase() === 'y') {
          console.log('\n🗑️ Deleting users...');
          
          for (const user of authData.users) {
            try {
              const { error } = await supabase.auth.admin.deleteUser(user.id);
              if (error) {
                console.log(`❌ Failed to delete ${user.email}:`, error.message);
              } else {
                console.log(`✅ Deleted ${user.email || user.id}`);
              }
            } catch (err) {
              console.log(`❌ Error deleting ${user.email}:`, err.message);
            }
          }
          
          console.log('\n🎉 Done!');
        } else {
          console.log('Cancelled');
        }
        
        rl.close();
      }
    }
    
  } catch (error) {
    console.log('❌ Unexpected error:', error.message);
    console.log('Stack:', error.stack);
  }
}

main();
