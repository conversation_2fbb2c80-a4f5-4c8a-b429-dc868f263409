-- =====================================================
-- MIGRATION SCRIPT: HARDCODED ENUMS TO DYNAMIC ENUMS
-- =====================================================
-- This script helps migrate from hardcoded CHECK constraints to dynamic enum system
-- Run this AFTER setting up the dynamic enum tables and populating initial data

-- =====================================================
-- 1. BACKUP EXISTING DATA (OPTIONAL)
-- =====================================================

-- Create backup tables (uncomment if needed)
-- CREATE TABLE backup_profiles AS SELECT * FROM profiles;
-- CREATE TABLE backup_doctors AS SELECT * FROM doctors;
-- CREATE TABLE backup_patients AS SELECT * FROM patients;
-- CREATE TABLE backup_appointments AS SELECT * FROM appointments;
-- CREATE TABLE backup_rooms AS SELECT * FROM rooms;
-- CREATE TABLE backup_medical_records AS SELECT * FROM medical_records;
-- CREATE TABLE backup_prescriptions AS SELECT * FROM prescriptions;

-- =====================================================
-- 2. REMOVE OLD CHECK CONSTRAINTS
-- =====================================================

-- Remove CHECK constraints from profiles table
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- Remove CHECK constraints from doctors table
ALTER TABLE doctors DROP CONSTRAINT IF EXISTS doctors_status_check;

-- Remove CHECK constraints from patients table
ALTER TABLE patients DROP CONSTRAINT IF EXISTS patients_gender_check;
ALTER TABLE patients DROP CONSTRAINT IF EXISTS patients_blood_type_check;
ALTER TABLE patients DROP CONSTRAINT IF EXISTS patients_status_check;

-- Remove CHECK constraints from appointments table
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS appointments_type_check;
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS appointments_status_check;

-- Remove CHECK constraints from rooms table
ALTER TABLE rooms DROP CONSTRAINT IF EXISTS rooms_room_type_check;
ALTER TABLE rooms DROP CONSTRAINT IF EXISTS rooms_status_check;

-- Remove CHECK constraints from medical_records table
ALTER TABLE medical_records DROP CONSTRAINT IF EXISTS medical_records_status_check;

-- Remove CHECK constraints from prescriptions table
ALTER TABLE prescriptions DROP CONSTRAINT IF EXISTS prescriptions_status_check;

-- =====================================================
-- 3. CREATE VALIDATION FUNCTIONS
-- =====================================================

-- Function to validate profile role
CREATE OR REPLACE FUNCTION validate_profile_role()
RETURNS TRIGGER AS $$
BEGIN
  IF NOT validate_enum_value('ROLE', NEW.role) THEN
    RAISE EXCEPTION 'Invalid role: %. Must be a valid enum value from ROLE category.', NEW.role;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate doctor status
CREATE OR REPLACE FUNCTION validate_doctor_status()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('DOCTOR_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid doctor status: %. Must be a valid enum value from DOCTOR_STATUS category.', NEW.status;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate patient data
CREATE OR REPLACE FUNCTION validate_patient_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate gender
  IF NEW.gender IS NOT NULL AND NOT validate_enum_value('GENDER', NEW.gender) THEN
    RAISE EXCEPTION 'Invalid gender: %. Must be a valid enum value from GENDER category.', NEW.gender;
  END IF;
  
  -- Validate blood type
  IF NEW.blood_type IS NOT NULL AND NOT validate_enum_value('BLOOD_TYPE', NEW.blood_type) THEN
    RAISE EXCEPTION 'Invalid blood type: %. Must be a valid enum value from BLOOD_TYPE category.', NEW.blood_type;
  END IF;
  
  -- Validate status
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('PATIENT_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid patient status: %. Must be a valid enum value from PATIENT_STATUS category.', NEW.status;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate appointment data
CREATE OR REPLACE FUNCTION validate_appointment_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate type
  IF NEW.type IS NOT NULL AND NOT validate_enum_value('APPOINTMENT_TYPE', NEW.type) THEN
    RAISE EXCEPTION 'Invalid appointment type: %. Must be a valid enum value from APPOINTMENT_TYPE category.', NEW.type;
  END IF;
  
  -- Validate status
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('APPOINTMENT_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid appointment status: %. Must be a valid enum value from APPOINTMENT_STATUS category.', NEW.status;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate room data
CREATE OR REPLACE FUNCTION validate_room_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate room type
  IF NEW.room_type IS NOT NULL AND NOT validate_enum_value('ROOM_TYPE', NEW.room_type) THEN
    RAISE EXCEPTION 'Invalid room type: %. Must be a valid enum value from ROOM_TYPE category.', NEW.room_type;
  END IF;
  
  -- Validate status
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('ROOM_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid room status: %. Must be a valid enum value from ROOM_STATUS category.', NEW.status;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate medical record status
CREATE OR REPLACE FUNCTION validate_medical_record_status()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('MEDICAL_RECORD_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid medical record status: %. Must be a valid enum value from MEDICAL_RECORD_STATUS category.', NEW.status;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate prescription status
CREATE OR REPLACE FUNCTION validate_prescription_status()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('PRESCRIPTION_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid prescription status: %. Must be a valid enum value from PRESCRIPTION_STATUS category.', NEW.status;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. CREATE VALIDATION TRIGGERS
-- =====================================================

-- Profiles table triggers
DROP TRIGGER IF EXISTS trigger_validate_profile_role ON profiles;
CREATE TRIGGER trigger_validate_profile_role
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION validate_profile_role();

-- Doctors table triggers
DROP TRIGGER IF EXISTS trigger_validate_doctor_status ON doctors;
CREATE TRIGGER trigger_validate_doctor_status
  BEFORE INSERT OR UPDATE ON doctors
  FOR EACH ROW EXECUTE FUNCTION validate_doctor_status();

-- Patients table triggers
DROP TRIGGER IF EXISTS trigger_validate_patient_data ON patients;
CREATE TRIGGER trigger_validate_patient_data
  BEFORE INSERT OR UPDATE ON patients
  FOR EACH ROW EXECUTE FUNCTION validate_patient_data();

-- Appointments table triggers
DROP TRIGGER IF EXISTS trigger_validate_appointment_data ON appointments;
CREATE TRIGGER trigger_validate_appointment_data
  BEFORE INSERT OR UPDATE ON appointments
  FOR EACH ROW EXECUTE FUNCTION validate_appointment_data();

-- Rooms table triggers
DROP TRIGGER IF EXISTS trigger_validate_room_data ON rooms;
CREATE TRIGGER trigger_validate_room_data
  BEFORE INSERT OR UPDATE ON rooms
  FOR EACH ROW EXECUTE FUNCTION validate_room_data();

-- Medical records table triggers
DROP TRIGGER IF EXISTS trigger_validate_medical_record_status ON medical_records;
CREATE TRIGGER trigger_validate_medical_record_status
  BEFORE INSERT OR UPDATE ON medical_records
  FOR EACH ROW EXECUTE FUNCTION validate_medical_record_status();

-- Prescriptions table triggers
DROP TRIGGER IF EXISTS trigger_validate_prescription_status ON prescriptions;
CREATE TRIGGER trigger_validate_prescription_status
  BEFORE INSERT OR UPDATE ON prescriptions
  FOR EACH ROW EXECUTE FUNCTION validate_prescription_status();

-- =====================================================
-- 5. DATA VALIDATION AND CLEANUP
-- =====================================================

-- Check for invalid data that doesn't match enum values
-- (Run these queries to identify data that needs to be fixed)

-- Check profiles with invalid roles
SELECT profile_id, role, 'Invalid role' as issue
FROM (
  SELECT 
    COALESCE(p.profile_id, p.id::text) as profile_id,
    p.role
  FROM profiles p
) profiles_check
WHERE NOT EXISTS (
  SELECT 1 FROM system_enums se 
  WHERE se.category_id = 'ROLE' 
    AND se.enum_key = profiles_check.role 
    AND se.is_active = true
);

-- Check doctors with invalid status
SELECT doctor_id, status, 'Invalid doctor status' as issue
FROM doctors d
WHERE d.status IS NOT NULL 
  AND NOT EXISTS (
    SELECT 1 FROM system_enums se 
    WHERE se.category_id = 'DOCTOR_STATUS' 
      AND se.enum_key = d.status 
      AND se.is_active = true
  );

-- Check patients with invalid data
SELECT patient_id, 
       CASE 
         WHEN gender_invalid THEN 'Invalid gender: ' || gender
         WHEN blood_type_invalid THEN 'Invalid blood type: ' || blood_type
         WHEN status_invalid THEN 'Invalid status: ' || status
       END as issue
FROM (
  SELECT 
    patient_id,
    gender,
    blood_type,
    status,
    (gender IS NOT NULL AND NOT EXISTS (
      SELECT 1 FROM system_enums se 
      WHERE se.category_id = 'GENDER' AND se.enum_key = gender AND se.is_active = true
    )) as gender_invalid,
    (blood_type IS NOT NULL AND NOT EXISTS (
      SELECT 1 FROM system_enums se 
      WHERE se.category_id = 'BLOOD_TYPE' AND se.enum_key = blood_type AND se.is_active = true
    )) as blood_type_invalid,
    (status IS NOT NULL AND NOT EXISTS (
      SELECT 1 FROM system_enums se 
      WHERE se.category_id = 'PATIENT_STATUS' AND se.enum_key = status AND se.is_active = true
    )) as status_invalid
  FROM patients
) patient_check
WHERE gender_invalid OR blood_type_invalid OR status_invalid;

-- =====================================================
-- 6. UPDATE DEFAULT VALUES USING DYNAMIC ENUMS
-- =====================================================

-- Update default values to use dynamic enum defaults
UPDATE profiles SET role = get_default_enum_value('ROLE') 
WHERE role IS NULL OR role = '';

UPDATE doctors SET status = get_default_enum_value('DOCTOR_STATUS') 
WHERE status IS NULL OR status = '';

UPDATE patients SET status = get_default_enum_value('PATIENT_STATUS') 
WHERE status IS NULL OR status = '';

UPDATE appointments SET 
  type = COALESCE(type, get_default_enum_value('APPOINTMENT_TYPE')),
  status = COALESCE(status, get_default_enum_value('APPOINTMENT_STATUS'))
WHERE type IS NULL OR status IS NULL;

UPDATE rooms SET status = get_default_enum_value('ROOM_STATUS') 
WHERE status IS NULL OR status = '';

UPDATE medical_records SET status = get_default_enum_value('MEDICAL_RECORD_STATUS') 
WHERE status IS NULL OR status = '';

UPDATE prescriptions SET status = get_default_enum_value('PRESCRIPTION_STATUS') 
WHERE status IS NULL OR status = '';

-- =====================================================
-- 7. CREATE HELPER VIEWS FOR EASY ENUM ACCESS
-- =====================================================

-- View for getting enum options with Vietnamese labels
CREATE OR REPLACE VIEW v_enum_options_vi AS
SELECT 
  category_id,
  enum_key as value,
  display_name_vi as label,
  description_vi as description,
  color_code,
  icon_name,
  sort_order,
  is_default
FROM system_enums
WHERE is_active = true
ORDER BY category_id, sort_order, display_name_vi;

-- View for getting enum options with English labels
CREATE OR REPLACE VIEW v_enum_options_en AS
SELECT 
  category_id,
  enum_key as value,
  display_name_en as label,
  description_en as description,
  color_code,
  icon_name,
  sort_order,
  is_default
FROM system_enums
WHERE is_active = true
ORDER BY category_id, sort_order, display_name_en;

-- =====================================================
-- 8. VERIFICATION QUERIES
-- =====================================================

-- Verify migration success
SELECT 
  'Migration completed successfully' as status,
  (SELECT COUNT(*) FROM enum_categories WHERE is_active = true) as active_categories,
  (SELECT COUNT(*) FROM system_enums WHERE is_active = true) as active_enums,
  (SELECT COUNT(*) FROM profiles WHERE role IS NOT NULL) as profiles_with_roles,
  (SELECT COUNT(*) FROM doctors WHERE status IS NOT NULL) as doctors_with_status;

-- Show enum statistics
SELECT 
  ec.category_name,
  ec.display_name_vi as category_display,
  COUNT(se.enum_id) as enum_count,
  COUNT(CASE WHEN se.is_default THEN 1 END) as default_count
FROM enum_categories ec
LEFT JOIN system_enums se ON ec.category_id = se.category_id AND se.is_active = true
WHERE ec.is_active = true
GROUP BY ec.category_id, ec.category_name, ec.display_name_vi
ORDER BY ec.category_name;
