#!/usr/bin/env node

/**
 * Script để xóa users trong Supabase Authentication (auth.users)
 * Chạy: node backend/scripts/delete-auth-users.js
 */

const path = require('path');
require('dotenv').config({
  path: path.resolve(__dirname, '../.env')
});
const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Kiểm tra biến môi trường
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('- SUPABASE_URL:', !!supabaseUrl);
  console.error('- SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  console.error('\n💡 Đảm bảo file backend/.env có các biến sau:');
  console.error('SUPABASE_URL=your_supabase_url');
  console.error('SUPABASE_SERVICE_ROLE_KEY=your_service_role_key');
  process.exit(1);
}

// Khởi tạo Supabase client với Service Role Key (cần thiết để xóa auth users)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Interface để nhận input từ user
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

function confirmAction(message) {
  return new Promise((resolve) => {
    rl.question(`${message} (y/N): `, (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function checkConnection() {
  log('blue', '🔗 Kiểm tra kết nối Supabase với Service Role...');

  try {
    // Test kết nối cơ bản trước
    log('blue', '📋 Test truy cập bảng profiles...');
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (profilesError) {
      log('yellow', `⚠️  Profiles: ${profilesError.message}`);
    } else {
      log('green', '✅ Profiles: Truy cập thành công');
    }

    // Test với Service Role Key
    log('blue', '🔐 Test truy cập auth admin...');
    const { data, error } = await supabase.auth.admin.listUsers();

    if (error) {
      log('red', `❌ Auth Admin Error: ${error.message}`);
      log('yellow', '💡 Các nguyên nhân có thể:');
      log('yellow', '   1. Service Role Key không đúng');
      log('yellow', '   2. Service Role Key không có quyền admin');
      log('yellow', '   3. URL Supabase không đúng');
      log('yellow', '   4. Vấn đề kết nối mạng');
      throw new Error(`Auth admin error: ${error.message}`);
    }

    log('green', '✅ Kết nối Supabase thành công với quyền admin');
    log('blue', `📊 Tìm thấy ${data.users.length} users trong auth.users`);
    return true;
  } catch (error) {
    log('red', `❌ Lỗi kết nối: ${error.message}`);
    log('yellow', '\n🔧 Hướng dẫn khắc phục:');
    log('yellow', '1. Kiểm tra file backend/.env có đúng không');
    log('yellow', '2. Đảm bảo SUPABASE_SERVICE_ROLE_KEY là service_role key (không phải anon key)');
    log('yellow', '3. Chạy: node test-supabase-connection.js để debug');
    return false;
  }
}

async function listAllAuthUsers() {
  log('cyan', '\n📋 Danh sách tất cả users trong auth.users:');

  try {
    const { data, error } = await supabase.auth.admin.listUsers();

    if (error) throw error;

    if (data.users.length === 0) {
      log('yellow', '📭 Không có users nào trong auth.users');
      return [];
    }

    data.users.forEach((user, index) => {
      const createdAt = new Date(user.created_at).toLocaleString('vi-VN');
      const lastSignIn = user.last_sign_in_at ?
        new Date(user.last_sign_in_at).toLocaleString('vi-VN') :
        'Chưa đăng nhập';

      log('blue', `${index + 1}. 📧 ${user.email || 'No email'}`);
      log('blue', `   🆔 ID: ${user.id}`);
      log('blue', `   📅 Tạo: ${createdAt}`);
      log('blue', `   🔐 Đăng nhập cuối: ${lastSignIn}`);
      log('blue', `   ✅ Xác thực email: ${user.email_confirmed_at ? 'Có' : 'Chưa'}`);
      console.log('');
    });

    return data.users;
  } catch (error) {
    log('red', `❌ Lỗi khi lấy danh sách users: ${error.message}`);
    return [];
  }
}

async function deleteAuthUser(userId, email) {
  try {
    log('yellow', `🗑️  Đang xóa auth user: ${email} (${userId})`);

    const { error } = await supabase.auth.admin.deleteUser(userId);

    if (error) {
      throw error;
    }

    log('green', `✅ Đã xóa thành công auth user: ${email}`);
    return true;
  } catch (error) {
    log('red', `❌ Lỗi khi xóa auth user ${email}: ${error.message}`);
    return false;
  }
}

async function deleteAllAuthUsers() {
  log('cyan', '\n🗑️  Bắt đầu xóa TẤT CẢ auth users...');

  const users = await listAllAuthUsers();

  if (users.length === 0) {
    log('yellow', '📭 Không có users nào để xóa');
    return true;
  }

  log('red', `⚠️  BẠN SẮP XÓA ${users.length} AUTH USERS!`);
  log('red', '⚠️  THAO TÁC NÀY KHÔNG THỂ HOÀN TÁC!');

  const confirmed = await confirmAction('Bạn có CHẮC CHẮN muốn xóa tất cả auth users?');
  if (!confirmed) {
    log('blue', 'Đã hủy thao tác');
    return false;
  }

  let successCount = 0;
  let failCount = 0;

  for (const user of users) {
    const success = await deleteAuthUser(user.id, user.email || 'No email');
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }

  log('cyan', `\n📊 Kết quả xóa auth users:`);
  log('green', `✅ Thành công: ${successCount} users`);
  log('red', `❌ Thất bại: ${failCount} users`);

  return failCount === 0;
}

async function deleteSpecificAuthUser() {
  log('cyan', '\n🗑️  Xóa auth user cụ thể...');

  const users = await listAllAuthUsers();

  if (users.length === 0) {
    log('yellow', '📭 Không có users nào để xóa');
    return false;
  }

  const choice = await askQuestion(`Nhập số thứ tự user muốn xóa (1-${users.length}): `);
  const index = parseInt(choice) - 1;

  if (isNaN(index) || index < 0 || index >= users.length) {
    log('red', 'Lựa chọn không hợp lệ');
    return false;
  }

  const selectedUser = users[index];
  const confirmed = await confirmAction(`Xóa user "${selectedUser.email || 'No email'}"?`);

  if (!confirmed) {
    log('blue', 'Đã hủy thao tác');
    return false;
  }

  return await deleteAuthUser(selectedUser.id, selectedUser.email || 'No email');
}

async function deleteAuthUsersByEmail() {
  log('cyan', '\n🗑️  Xóa auth user theo email...');

  const email = await askQuestion('Nhập email của user muốn xóa: ');

  if (!email.trim()) {
    log('red', 'Email không được để trống');
    return false;
  }

  try {
    // Tìm user theo email
    const { data, error } = await supabase.auth.admin.listUsers();

    if (error) throw error;

    const user = data.users.find(u => u.email === email.trim());

    if (!user) {
      log('red', `❌ Không tìm thấy user với email: ${email}`);
      return false;
    }

    const confirmed = await confirmAction(`Xóa user "${user.email}" (ID: ${user.id})?`);

    if (!confirmed) {
      log('blue', 'Đã hủy thao tác');
      return false;
    }

    return await deleteAuthUser(user.id, user.email);
  } catch (error) {
    log('red', `❌ Lỗi: ${error.message}`);
    return false;
  }
}

async function deleteAuthUsersAndProfiles() {
  log('cyan', '\n🗑️  Xóa TẤT CẢ auth users VÀ profiles...');
  log('red', '⚠️  THAO TÁC NÀY SẼ XÓA TOÀN BỘ DỮ LIỆU NGƯỜI DÙNG!');

  const confirmed = await confirmAction('Bạn có CHẮC CHẮN muốn xóa tất cả users và profiles?');
  if (!confirmed) {
    log('blue', 'Đã hủy thao tác');
    return false;
  }

  // Bước 1: Xóa dữ liệu phụ thuộc
  log('yellow', '🗑️  Bước 1: Xóa dữ liệu phụ thuộc...');
  const tables = ['medical_records', 'prescriptions', 'appointments', 'patients', 'doctors', 'admins'];

  for (const table of tables) {
    try {
      const { error } = await supabase.from(table).delete().neq('id', 'dummy');
      if (error) {
        log('yellow', `⚠️  Không thể xóa ${table}: ${error.message}`);
      } else {
        log('green', `✅ Đã xóa dữ liệu từ ${table}`);
      }
    } catch (err) {
      log('yellow', `⚠️  Bảng ${table} có thể không tồn tại`);
    }
  }

  // Bước 2: Xóa profiles
  log('yellow', '🗑️  Bước 2: Xóa profiles...');
  try {
    const { error } = await supabase.from('profiles').delete().neq('id', 'dummy');
    if (error) {
      log('red', `❌ Lỗi khi xóa profiles: ${error.message}`);
    } else {
      log('green', '✅ Đã xóa tất cả profiles');
    }
  } catch (err) {
    log('yellow', '⚠️  Không thể xóa profiles');
  }

  // Bước 3: Xóa auth users
  log('yellow', '🗑️  Bước 3: Xóa auth users...');
  return await deleteAllAuthUsers();
}

async function main() {
  try {
    log('cyan', '🔐 SCRIPT XÓA AUTH USERS - SUPABASE AUTHENTICATION');
    log('red', '⚠️  CẢNH BÁO: Script này sẽ xóa users trong auth.users vĩnh viễn!');

    // Kiểm tra kết nối và quyền
    const connected = await checkConnection();
    if (!connected) {
      process.exit(1);
    }

    // Menu lựa chọn
    console.log('\n📋 Lựa chọn hành động:');
    console.log('1. Xem danh sách tất cả auth users');
    console.log('2. Xóa TẤT CẢ auth users');
    console.log('3. Xóa auth user cụ thể (chọn từ danh sách)');
    console.log('4. Xóa auth user theo email');
    console.log('5. Xóa TẤT CẢ auth users VÀ profiles (NGUY HIỂM!)');
    console.log('6. Hủy bỏ');

    const choice = await askQuestion('\nNhập lựa chọn (1-6): ');

    switch (choice) {
      case '1':
        await listAllAuthUsers();
        break;

      case '2':
        await deleteAllAuthUsers();
        break;

      case '3':
        await deleteSpecificAuthUser();
        break;

      case '4':
        await deleteAuthUsersByEmail();
        break;

      case '5':
        await deleteAuthUsersAndProfiles();
        break;

      case '6':
        log('blue', 'Đã hủy thao tác');
        break;

      default:
        log('red', 'Lựa chọn không hợp lệ');
        break;
    }

    // Hiển thị kết quả cuối
    log('cyan', '\n📊 Kiểm tra auth users sau khi thực hiện:');
    await listAllAuthUsers();

  } catch (error) {
    log('red', `❌ Lỗi: ${error.message}`);
  } finally {
    rl.close();
  }
}

// Chạy script
if (require.main === module) {
  main();
}

module.exports = {
  deleteAllAuthUsers,
  deleteAuthUser,
  listAllAuthUsers,
  deleteAuthUsersAndProfiles
};
