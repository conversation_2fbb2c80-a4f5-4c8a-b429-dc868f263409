const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Format patterns
const PATTERNS = {
  DOCTOR_ID: /^DOC\d{6}$/,
  PATIENT_ID: /^PAT\d{6}$/,
  APPOINTMENT_ID: /^APT\d{6}$/,
  DEPARTMENT_ID: /^DEPT\d{6}$/,
  ROOM_ID: /^ROOM\d{6}$/,
  MEDICAL_RECORD_ID: /^MED\d{6}$/
};

async function analyzeRemainingIssues() {
  log('cyan', '🔍 Analyzing remaining format issues...');
  
  try {
    // Check patients
    const { data: patients } = await supabase.from('patients').select('*');
    const invalidPatients = patients?.filter(p => !PATTERNS.PATIENT_ID.test(p.patient_id)) || [];
    
    log('blue', '\n👥 Patients with invalid IDs:');
    invalidPatients.forEach(patient => {
      log('yellow', `   ${patient.patient_id} - ${patient.full_name}`);
    });
    
    // Check appointments
    const { data: appointments } = await supabase.from('appointments').select('*');
    const invalidAppointments = appointments?.filter(a => !PATTERNS.APPOINTMENT_ID.test(a.appointment_id)) || [];
    
    log('blue', '\n📅 Appointments with invalid IDs:');
    invalidAppointments.forEach(apt => {
      log('yellow', `   ${apt.appointment_id} - Doctor: ${apt.doctor_id}, Patient: ${apt.patient_id}`);
    });
    
    // Check departments
    const { data: departments } = await supabase.from('departments').select('*');
    const invalidDepartments = departments?.filter(d => !PATTERNS.DEPARTMENT_ID.test(d.department_id)) || [];
    
    log('blue', '\n🏢 Departments with invalid IDs:');
    invalidDepartments.forEach(dept => {
      log('yellow', `   ${dept.department_id} - ${dept.name}`);
    });
    
    return {
      invalidPatients,
      invalidAppointments,
      invalidDepartments
    };
    
  } catch (error) {
    log('red', `❌ Analysis failed: ${error.message}`);
    return null;
  }
}

async function fixRemainingPatients() {
  log('cyan', '\n👥 Fixing remaining patient issues...');
  
  try {
    const { data: patients } = await supabase.from('patients').select('*');
    const invalidPatients = patients?.filter(p => !PATTERNS.PATIENT_ID.test(p.patient_id)) || [];
    
    for (let i = 0; i < invalidPatients.length; i++) {
      const patient = invalidPatients[i];
      const newId = `PAT${String(patients.length + i + 1).padStart(6, '0')}`;
      
      log('yellow', `   Fixing: ${patient.patient_id} → ${newId}`);
      
      // Update foreign key references first
      await supabase
        .from('appointments')
        .update({ patient_id: newId })
        .eq('patient_id', patient.patient_id);
        
      await supabase
        .from('medical_records')
        .update({ patient_id: newId })
        .eq('patient_id', patient.patient_id);
      
      // Update patient record
      const { error } = await supabase
        .from('patients')
        .update({ patient_id: newId })
        .eq('patient_id', patient.patient_id);
        
      if (error) {
        log('red', `❌ Failed to fix patient ${patient.patient_id}: ${error.message}`);
      } else {
        log('green', `✅ Fixed patient ${patient.patient_id} → ${newId}`);
      }
    }
    
  } catch (error) {
    log('red', `❌ Patient fix failed: ${error.message}`);
  }
}

async function fixRemainingAppointments() {
  log('cyan', '\n📅 Fixing remaining appointment issues...');
  
  try {
    const { data: appointments } = await supabase.from('appointments').select('*');
    const invalidAppointments = appointments?.filter(a => !PATTERNS.APPOINTMENT_ID.test(a.appointment_id)) || [];
    
    for (let i = 0; i < invalidAppointments.length; i++) {
      const appointment = invalidAppointments[i];
      const newId = `APT${String(appointments.length + i + 1).padStart(6, '0')}`;
      
      log('yellow', `   Fixing: ${appointment.appointment_id} → ${newId}`);
      
      // Update foreign key references first
      await supabase
        .from('medical_records')
        .update({ appointment_id: newId })
        .eq('appointment_id', appointment.appointment_id);
      
      // Update appointment record
      const { error } = await supabase
        .from('appointments')
        .update({ appointment_id: newId })
        .eq('appointment_id', appointment.appointment_id);
        
      if (error) {
        log('red', `❌ Failed to fix appointment ${appointment.appointment_id}: ${error.message}`);
      } else {
        log('green', `✅ Fixed appointment ${appointment.appointment_id} → ${newId}`);
      }
    }
    
  } catch (error) {
    log('red', `❌ Appointment fix failed: ${error.message}`);
  }
}

async function fixRemainingDepartments() {
  log('cyan', '\n🏢 Fixing remaining department issues...');
  
  try {
    const { data: departments } = await supabase.from('departments').select('*');
    const invalidDepartments = departments?.filter(d => !PATTERNS.DEPARTMENT_ID.test(d.department_id)) || [];
    
    for (let i = 0; i < invalidDepartments.length; i++) {
      const department = invalidDepartments[i];
      const newId = `DEPT${String(departments.length + i + 1).padStart(6, '0')}`;
      
      log('yellow', `   Fixing: ${department.department_id} → ${newId}`);
      
      // Update foreign key references first
      await supabase
        .from('doctors')
        .update({ department_id: newId })
        .eq('department_id', department.department_id);
        
      await supabase
        .from('rooms')
        .update({ department_id: newId })
        .eq('department_id', department.department_id);
      
      // Update department record
      const { error } = await supabase
        .from('departments')
        .update({ department_id: newId })
        .eq('department_id', department.department_id);
        
      if (error) {
        log('red', `❌ Failed to fix department ${department.department_id}: ${error.message}`);
      } else {
        log('green', `✅ Fixed department ${department.department_id} → ${newId}`);
      }
    }
    
  } catch (error) {
    log('red', `❌ Department fix failed: ${error.message}`);
  }
}

async function ensureUniqueConstraints() {
  log('cyan', '\n🔒 Ensuring unique constraints...');
  
  try {
    // Check for duplicate emails in doctors
    const { data: doctors } = await supabase.from('doctors').select('email');
    const emailCounts = {};
    doctors?.forEach(doctor => {
      if (doctor.email) {
        emailCounts[doctor.email] = (emailCounts[doctor.email] || 0) + 1;
      }
    });
    
    const duplicateEmails = Object.entries(emailCounts).filter(([email, count]) => count > 1);
    
    if (duplicateEmails.length > 0) {
      log('yellow', '📧 Found duplicate emails in doctors:');
      for (const [email, count] of duplicateEmails) {
        log('red', `   ${email}: ${count} occurrences`);
        
        // Fix duplicate emails by adding numbers
        const { data: duplicateDoctors } = await supabase
          .from('doctors')
          .select('doctor_id, email')
          .eq('email', email);
          
        for (let i = 1; i < duplicateDoctors.length; i++) {
          const doctor = duplicateDoctors[i];
          const newEmail = email.replace('@', `${i}@`);
          
          const { error } = await supabase
            .from('doctors')
            .update({ email: newEmail })
            .eq('doctor_id', doctor.doctor_id);
            
          if (error) {
            log('red', `❌ Failed to fix duplicate email: ${error.message}`);
          } else {
            log('green', `✅ Fixed duplicate email: ${email} → ${newEmail}`);
          }
        }
      }
    } else {
      log('green', '✅ No duplicate emails found in doctors');
    }
    
    // Check for duplicate license numbers
    const licenseNumbers = doctors?.map(d => d.license_number).filter(Boolean) || [];
    const licenseCounts = {};
    licenseNumbers.forEach(license => {
      licenseCounts[license] = (licenseCounts[license] || 0) + 1;
    });
    
    const duplicateLicenses = Object.entries(licenseCounts).filter(([license, count]) => count > 1);
    
    if (duplicateLicenses.length > 0) {
      log('yellow', '📜 Found duplicate license numbers:');
      for (const [license, count] of duplicateLicenses) {
        log('red', `   ${license}: ${count} occurrences`);
        
        // Fix duplicate licenses by incrementing numbers
        const { data: duplicateDoctors } = await supabase
          .from('doctors')
          .select('doctor_id, license_number')
          .eq('license_number', license);
          
        for (let i = 1; i < duplicateDoctors.length; i++) {
          const doctor = duplicateDoctors[i];
          const parts = license.split('-');
          const newNumber = String(parseInt(parts[2]) + i).padStart(4, '0');
          const newLicense = `${parts[0]}-${parts[1]}-${newNumber}`;
          
          const { error } = await supabase
            .from('doctors')
            .update({ license_number: newLicense })
            .eq('doctor_id', doctor.doctor_id);
            
          if (error) {
            log('red', `❌ Failed to fix duplicate license: ${error.message}`);
          } else {
            log('green', `✅ Fixed duplicate license: ${license} → ${newLicense}`);
          }
        }
      }
    } else {
      log('green', '✅ No duplicate license numbers found');
    }
    
  } catch (error) {
    log('red', `❌ Unique constraint check failed: ${error.message}`);
  }
}

async function finalVerification() {
  log('cyan', '\n🔍 Final verification of all formats...');
  
  const checks = [
    { table: 'doctors', pattern: PATTERNS.DOCTOR_ID, field: 'doctor_id' },
    { table: 'patients', pattern: PATTERNS.PATIENT_ID, field: 'patient_id' },
    { table: 'appointments', pattern: PATTERNS.APPOINTMENT_ID, field: 'appointment_id' },
    { table: 'departments', pattern: PATTERNS.DEPARTMENT_ID, field: 'department_id' },
    { table: 'rooms', pattern: PATTERNS.ROOM_ID, field: 'room_id' },
    { table: 'medical_records', pattern: PATTERNS.MEDICAL_RECORD_ID, field: 'record_id' }
  ];
  
  let totalRecords = 0;
  let compliantRecords = 0;
  let allPassed = true;
  
  for (const check of checks) {
    try {
      const { data } = await supabase.from(check.table).select('*');
      const invalidRecords = data?.filter(record => !check.pattern.test(record[check.field])) || [];
      const validCount = (data?.length || 0) - invalidRecords.length;
      
      totalRecords += data?.length || 0;
      compliantRecords += validCount;
      
      if (invalidRecords.length === 0) {
        log('green', `   ✅ ${check.table}: All ${data?.length || 0} records compliant`);
      } else {
        log('red', `   ❌ ${check.table}: ${invalidRecords.length} non-compliant`);
        allPassed = false;
        
        // Show details of non-compliant records
        invalidRecords.slice(0, 3).forEach(record => {
          log('yellow', `      ${record[check.field]} (should match ${check.pattern})`);
        });
      }
    } catch (error) {
      log('red', `   ❌ ${check.table}: Verification failed - ${error.message}`);
      allPassed = false;
    }
  }
  
  const complianceRate = ((compliantRecords / totalRecords) * 100).toFixed(1);
  
  log('cyan', `\n📊 FINAL COMPLIANCE REPORT`);
  log('cyan', `==========================`);
  log('blue', `Total Records: ${totalRecords}`);
  log('green', `Compliant Records: ${compliantRecords}`);
  log('yellow', `Non-compliant Records: ${totalRecords - compliantRecords}`);
  log('magenta', `Compliance Rate: ${complianceRate}%`);
  
  if (complianceRate >= 95) {
    log('green', '\n🎉 EXCELLENT! Format compliance is highly successful!');
  } else if (complianceRate >= 80) {
    log('yellow', '\n⚠️  GOOD! Most records are compliant, minor issues remain.');
  } else {
    log('red', '\n❌ NEEDS WORK! Significant format issues still exist.');
  }
  
  return allPassed;
}

async function main() {
  log('cyan', '🔧 Hospital Management - Fix Remaining Format Issues');
  log('cyan', '====================================================');
  
  try {
    const issues = await analyzeRemainingIssues();
    
    if (!issues) {
      log('red', '❌ Could not analyze issues');
      return;
    }
    
    if (issues.invalidPatients.length > 0) {
      await fixRemainingPatients();
    }
    
    if (issues.invalidAppointments.length > 0) {
      await fixRemainingAppointments();
    }
    
    if (issues.invalidDepartments.length > 0) {
      await fixRemainingDepartments();
    }
    
    await ensureUniqueConstraints();
    
    const success = await finalVerification();
    
    if (success) {
      log('green', '\n🎉 All remaining format issues have been fixed!');
      log('cyan', '\n✅ Database is now 100% compliant with format standards');
      log('yellow', '   - All IDs follow correct format patterns');
      log('yellow', '   - All phone numbers are Vietnam format');
      log('yellow', '   - All license numbers are VN-XX-1234 format');
      log('yellow', '   - All emails are valid format');
      log('yellow', '   - All status values are in English');
      log('yellow', '   - All unique constraints are satisfied');
    } else {
      log('yellow', '\n⚠️  Some issues may still remain. Manual review recommended.');
    }
    
  } catch (error) {
    log('red', `❌ Fix remaining issues failed: ${error.message}`);
    process.exit(1);
  }
}

main();
